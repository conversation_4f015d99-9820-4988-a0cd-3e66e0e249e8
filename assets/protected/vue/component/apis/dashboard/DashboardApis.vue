<template>
  <Card v-if="informationArray?.length > 0" card-class="primary mt20">
    <template #title>
      <i class="fa fa-bullhorn"></i>
      {{ t('apis/unreadInformation') }}
    </template>
    <template #body>
      <ul class="list-group recent-comments">
        <li
          v-for="info in informationArray"
          :key="info.information.number"
          class="list-group-item clearfix"
          :class="{
            'comment-default': info.sectionPrefix === 'VH',
            'comment-info': info.sectionPrefix === 'MH',
            'comment-warning': info.sectionPrefix === 'SH',
          }">
          <p class="text-ellipsis">
            <span class="strong">
              {{ info.sectionPrefix }} {{ info.information.number }} - {{ info.information.title }}
            </span>
          </p>
          <p v-html="`${info.information.contentShort}...`"></p>
          <a class="pull-right" :href="`/apis/${info.information.section}/${info.information.number}`">
            <button class="btn btn-primary btn-sm" v-html="t('apis/readMore')"></button>
          </a>
        </li>
      </ul>
    </template>
  </Card>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

import Card from '@assets/protected/vue/component/helper/Card.vue';

import { useModalStore } from '@assets/protected/vue/stores';

interface Information {
  number: number;
  title: string;
  contentShort: string;
  section: string;
}

interface InformationBuilder {
  sectionPrefix: string;
  information: Information;
}

const informationArray = ref<InformationBuilder[]>([]);
const loaded = ref<boolean>(true);

const modalStore = useModalStore();

const loadInformation = async () => {
  try {
    const response = await axios.get('/apis/dashboard');
    informationArray.value = response.data;
    loaded.value = true;
  } catch (error: any) {
    modalStore.showModal({
      cssClass: 'error',
      message: error?.response?.data || 'Error loading information',
    });
  }
};

onMounted(loadInformation);
</script>
