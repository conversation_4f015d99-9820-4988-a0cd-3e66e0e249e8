<template>
  <div>
    <b-form-group label="Kategorie">
      <b-form-select v-model="category" class="mb-1">
        <b-form-select-option v-for="item in categories" :key="item.id" :value="item" v-html="item.niceName" />
      </b-form-select>
    </b-form-group>

    <b-form-group v-if="category" label="Veranstaltung">
      <b-form-select v-model="event" class="mb-1">
        <b-form-select-option v-for="evt in events" :key="evt.id" :value="evt" v-html="evt.name" />
      </b-form-select>
    </b-form-group>

    <b-form-group v-if="event" label="Datum (Optional)">
      <b-form-select v-model="bookingDate" class="mb-1">
        <b-form-select-option :value="null" />
        <b-form-select-option v-for="item in dates" :key="item.id" :value="item.date" v-html="item.date" />
      </b-form-select>
    </b-form-group>

    <b-form-group v-if="event">
      <b-form-checkbox id="pastDataShow" v-model="pastDataShow" class="mb-1" name="pastDataShow">
        Nur zukünftige Buchungen
      </b-form-checkbox>
    </b-form-group>

    <b-form-group v-if="event">
      <b-button block variant="primary" @click.prevent="fetchBookings">Buchungen anzeigen</b-button>
    </b-form-group>

    <b-form-group v-if="show && !loading">
      <b-button
        block
        variant="success"
        :href="
          path + '/' + JSON.stringify({ pastDataShow: pastDataShow, eventID: event?.id, bookingDate: bookingDate })
        ">
        Export als CSV
      </b-button>
    </b-form-group>

    <b-form-group v-if="show && !loading" label="E-Mail Adressen">
      <b-form-textarea ref="emailsRef" class="mb-2" :value="emails" v-on:focus="$event.target.select()" readonly />
      <b-button block variant="info" @click.prevent="copy">Kopieren</b-button>
    </b-form-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useBookingsStore } from '@assets/protected/vue/stores/useBookingsStore';
import { useScroll } from '@assets/protected/vue/composables/useScroll';

const { scrollToSelector } = useScroll({ offset: -150 });

const props = withDefaults(
  defineProps<{
    path: string;
    exportEmailsPath: string;
  }>(),
  {
    path: '',
    exportEmailsPath: '',
  },
);

const bookingsStore = useBookingsStore();

const categories = computed(() => bookingsStore.getCategories);
const events = computed(() => bookingsStore.getEvents);
const show = computed(() => bookingsStore.getShowTable);
const loading = computed(() => bookingsStore.getLoading);
const dates = computed(() => bookingsStore.getDates);
const emails = computed(() => bookingsStore.getEmails);

const bookingDate = computed({
  get: () => bookingsStore.getBookingDate,
  set: (value: string | null) => {
    bookingsStore.setBookingDate(value || '');
  },
});

const category = ref<any | false>(false);
const event = ref<any | false>(false);
const pastDataShow = ref<boolean>(true);

const emailsRef = ref<HTMLTextAreaElement | null>(null);

onMounted(() => {
  bookingsStore.fetchCategories();
});

watch(category, (val) => {
  if (val) bookingsStore.fetchEvents(val);
});

watch(event, (val) => {
  bookingsStore.setEvent(val);
});

watch(pastDataShow, (val) => {
  bookingsStore.setPastDataShow(val);
});

const fetchBookings = () => {
  if (category.value && event.value) {
    bookingsStore.fetchBookings({
      pastDataShow: pastDataShow.value,
      eventID: event.value.id,
      bookingDate: bookingDate.value,
    });
    fetchEmails();
    scrollToSelector('#bookingsTool');
  } else {
    bookingsStore.setErrorMessage('Es fehlen Daten, um die Abfrage zu machen!');
  }
};

const fetchEmails = async () => {
  await bookingsStore.fetchEmails(
    props.exportEmailsPath +
      '/' +
      JSON.stringify({
        pastDataShow: pastDataShow.value,
        eventID: event.value?.id,
        bookingDate: bookingDate.value,
      }),
  );
};

const copy = () => {
  navigator.clipboard.writeText(emails.value);
  emailsRef.value?.focus();
};
</script>
