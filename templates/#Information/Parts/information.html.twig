<div id="singleInformation" ng-hide="loading" ng-cloak>

    <div id="page-header" class="clearfix">
        <div class="container-fluid noPadding noMargin">
            <div class="col-md-8">
                <h2 ng-bind-html="information.number_prefix + ' - ' + information.number + ' ' + information.title"></h2>
            </div>
            <div class="col-md-4 responsible">
                <div class="container-fluid">
                    <div class="col-md-4" ng-if="information.author_image">
                        <img ng-src="information.author_image" title="{% verbatim %}{{ information.author }}{% endverbatim %}" />
                    </div>
                    <div ng-class="{'col-md-8': information.author_image, 'col-md-12': !information.author_image}" class="info">
                        <div>
                            {{ "Gummersbach"|trans({}, 'information') }}, {% verbatim %}{{ information.release_date }}{% endverbatim %}<br />
                            {{ "Responsible for content and wording"|trans({}, 'information') }}: {% verbatim %}{{ information.author }}{% endverbatim %}<br />
                            {{ "eMail"|trans({}, 'information') }}: <a href="mailto:{% verbatim %}{{ information.author_email }}{% endverbatim %}">{% verbatim %}{{ information.author_email }}{% endverbatim %}</a>
                        </div>
                        <div class="actions">
                            <div><i class="fa fa-file-pdf-o fa-lg" title="{{ "Save as PDF"|trans({}, 'information') }}"></i></div>
                            <div><i class="fa fa-print fa-lg" title="{{ "Print"|trans({}, 'information') }}"></i></div>
                            <div ng-controller="InformationController" ng-init="setFavorite(information.number, information.isFavorite)">
                                <img class="removeFavorite" src="{{ asset('/dist/images/information/favorite.png') }}" title="{{ "Remove from favorite list"|trans({}, 'information') }}" ng-click="removeFavorite(information.section, information.number); $event.preventDefault();" ng-show="favorites[information.number]" />
                                <img class="addFavorite" src="{{ asset('/dist/images/information/nofavorite.png') }}" title="{{ "Add to favorite list"|trans({}, 'information') }}" ng-click="addFavorite(information.section, information.number); $event.preventDefault();" ng-hide="favorites[information.number]"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert alert-danger" style="margin-top: 20px; margin-bottom: 0;" ng-if="information.isRetracted">
        {{ 'ABUS Sales Information has been retracted.'|trans({}, module) }}
    </div>



    {#{% if message is iterable and message|length > 0 %}#}
        {#{% if message.type == 'error' %}#}
            {#<div class="alert alert-danger" style="margin-top: 20px; margin-bottom: 0;">#}
                {#{{ message.text }}#}
            {#</div>#}
        {#{% endif %}#}
    {#{% endif %}#}


    {#{% if salesInformation.draft or not salesInformation.published %}#}
        <div class="alert alert-danger" style="margin-top: 20px; margin-bottom: 0;" ng-show="information.isDraft">
            {{ "_is_draft"|trans({}, module) }}
        </div>

        <div class="alert alert-danger" style="margin-top: 20px; margin-bottom: 0;" ng-show="!information.isDraft && !information.isPublished">
            {{ "_not_published"|trans({}, module) }}
        </div>

        {#{% if salesInformation.getInternalMemo() != '' %}#}
            {#<div class="alert alert-info" style="margin-top: 20px; margin-bottom: 0;">#}
                {#{{ salesInformation.getInternalMemo() }}#}
            {#</div>#}
        {#{% endif %}#}

        {#<div class="panel panel-primary" style="margin-top: 20px; margin-bottom: 20px;">#}
            {#<div class="panel-heading">#}
                {#<h4 class="panel-title">{{ "Receivers"|trans({}, 'sales_common') }}</h4>#}
            {#</div>#}
            {#<div class="panel-body">#}
                {#{% for receiver, value in salesInformation.receivers %}#}
                    {#{% if value and salesInformation.isUserRole(receiver) %}#}
                        {#<i class="fa fa-check"></i> {{ receiver|trans({}, module) }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#}
                    {#{% endif %}#}
                {#{% endfor %}#}

                {#{% if salesInformation.getSendMail() == 'true' %}<img src="{{ asset('bundles/abusframe/img/flags/email.png') }}" />&nbsp;&nbsp;&nbsp;{{ 'Inform by mail'|trans({}, 'sales') }}{% endif %}#}
            {#</div>#}
        {#</div>#}
    {#{% endif %}#}

    {#<div class="responsible" style="position: absolute; right: 20px;">#}
        {#<div class="container-fluid">#}
            {#<div class="col-md-4" ng-if="information.author_image">#}
                {#<img ng-src="information.author_image" title="{% verbatim %}{{ information.author }}{% endverbatim %}" />#}
            {#</div>#}
            {#<div ng-class="{'col-md-8': information.author_image,  'col-md-12': !information.author_image}" class="info">#}
                {#{{ "Gummersbach"|trans({}, 'information') }}, {% verbatim %}{{ information.release_date }}{% endverbatim %}<br />#}
                {#{{ "Responsible for content and wording"|trans({}, 'information') }}: {% verbatim %}{{ information.author }}{% endverbatim %}<br />#}
                {#{{ "eMail"|trans({}, 'information') }}: <a href="mailto:{% verbatim %}{{ information.author_email }}{% endverbatim %}">{% verbatim %}{{ information.author_email }}{% endverbatim %}</a>#}
            {#</div>#}
        {#</div>#}
    {#</div>#}

    <div class="container-fluid">


        <div>{{ "Dear Madam or Sir,"|trans({}, 'information') }}</div>
        <div ng-bind-html="information.content"></div>
        <div>{{ "With best regards,"|trans({}, 'information') }}</div>
        <div>{{ "ABUS Kransysteme GmbH"|trans({}, 'information') }}</div>
        <div style="font-family: 'Comic Sans MS', Cantarell, 'Droid Sans', Verdana, sans-serif" ng-bind-html="information.author"></div>


        <div class="dokumente" ng-if="information.hasAttachments">

            <li ng-repeat="attachment in information.attachments">
                <div><img ng-src="/dist/images/information/filetypes/{% verbatim %}{{ attachment.extension }}{% endverbatim %}.png"></div>
                <div ng-bind-html="attachment.title"></div>
            </li>
        </div>

        <div style="clear: both;"></div>

        <div class="row footer" ng-if="information.categories">
            <div class="col-md-1">
                {{ "Categories"|trans({}, 'information') }}:
            </div>
            <div class="col-md-10">
                <span class="label label-default mr10 mb10" ng-repeat="category in information.categories" ng-bind-html="category"></span>
            </div>
        </div>

        <div class="reference bs-callout bs-callout-info fade in" ng-show="information.references">

            <div class="row" ng-repeat="reference in information.references" ng-class="{draft: reference.draft, notPublished: !reference.published}">
                {% include 'Information/Parts/reference.html.twig' %}
            </div>
        </div>

    </div>
</div>
