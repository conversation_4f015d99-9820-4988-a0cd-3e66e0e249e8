import { defineConfig, type UserConfig, type ConfigEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueDevTools from 'vite-plugin-vue-devtools';
import path from 'path';
import { VitePWA } from 'vite-plugin-pwa';
import Copy from 'rollup-plugin-copy';
import { createHtmlPlugin } from 'vite-plugin-html';
import inject from '@rollup/plugin-inject';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const paths = {
  build: 'public/build',
  images: 'assets/public/images/**/*',
  fonts: 'assets/fonts/**/*',
};

export default defineConfig((env: ConfigEnv) => {
  const isProduction = env.mode === 'production';

  const config: UserConfig = {
    base: '/build/',
    publicDir: 'public/static',

    build: {
      outDir: paths.build,
      sourcemap: !isProduction,
      emptyOutDir: true,
      manifest: 'manifest.json',
      cssCodeSplit: true,
      minify: isProduction ? 'terser' : false,

      terserOptions: {
        ecma: 5,
        compress: {
          drop_console: isProduction,
        },
        output: {
          comments: false,
        },
      },

      rollupOptions: {
        input: {
          'assets/scss/main': 'assets/scss/main.scss',
          //'assets/public/login/login': 'assets/public/login/login.ts',
          //'assets/public/resetPassword/resetPassword': 'assets/public/resetPassword/resetPassword.ts',
          'assets/public/app/app': 'assets/public/app/app.ts',
          'assets/public/layout/layout': 'assets/public/layout/layout.ts',
          'assets/public/form/form': 'assets/public/form/form.ts',
          'assets/public/vendor/vendor': 'assets/public/vendor/vendor.ts',
          'assets/public/register/register': 'assets/public/register/register.ts',
          'assets/protected/vue/controller/appController': 'assets/protected/vue/controller/appController.ts',
          'assets/protected/technicalData/technicalData': 'assets/protected/technicalData/technicalData.ts',
        },

        output: {
          entryFileNames: isProduction ? 'js/[name].[hash].js' : 'js/[name].js',
          chunkFileNames: isProduction ? 'js/[name].[hash].js' : 'js/[name].js',
          assetFileNames: (assetInfo) => {
            const originalName = assetInfo.originalFileNames?.[0] || '';
            const ext = path.extname(originalName);

            // Handle images
            if (/\.(png|jpe?g|gif|svg|ico)$/.test(ext)) {
              const filePath = assetInfo.name || '';
              // Similar image path handling as in webpack config
              const assetPath = filePath
                .replace(/public(\/images)?\//, '')
                .replace('assets/', 'images/')
                .replace('node_modules/', 'images/public/node_modules/')
                .replace(/\/{2,}/, '/')
                .replace(/(?!.*\/).+/, '');

              return isProduction ? `${assetPath}[name].[hash][extname]` : `${assetPath}[name][extname]`;
            }

            // Handle fonts
            if (/\.(woff|woff2|eot|ttf|otf)$/.test(ext)) {
              return isProduction ? 'fonts/[name].[hash][extname]' : 'fonts/[name][extname]';
            }

            // Default
            return isProduction ? '[name].[hash][extname]' : '[name][extname]';
          },

          manualChunks(id) {
            if (id.includes('node_modules')) {
              // Handle specific problematic packages with direct paths
              if (id.includes('@fortawesome/vue-fontawesome')) {
                return 'vendor-fontawesome';
              }
              return 'vendor';
            }
          },

          globals: {
            jquery: 'jQuery',
            $: 'jQuery',
          },
        },
      },
    },

    resolve: {
      extensions: ['.ts', '.js', '.vue'],
      alias: {
        jquery: path.resolve(__dirname, 'node_modules/jquery'),
        '@root': path.resolve(__dirname),
        '@assets': path.resolve(__dirname, 'assets/'),
        '@node_modules': path.resolve(__dirname, 'node_modules/'),
        Assets: path.resolve(__dirname, 'assets/'),
        Translations: path.resolve(__dirname, 'translations/'),
        '@translations': path.resolve(__dirname, 'translations/'),
        EventBuses: path.resolve(__dirname, 'assets/protected/vue/eventBuses/'),
        Flags: path.resolve(__dirname, 'node_modules/svg-country-flags/'),
        '@flags': path.resolve(__dirname, 'node_modules/svg-country-flags/'),
        // Fix for @fortawesome/vue-fontawesome
        '@fortawesome/vue-fontawesome': path.resolve(__dirname, 'node_modules/@fortawesome/vue-fontawesome/index.js'),
        // Add aliases for bootstrap SCSS imports with tilde
        '~bootstrap': path.resolve(__dirname, 'node_modules/bootstrap'),
        bootstrap: path.resolve(__dirname, 'node_modules/bootstrap'),
        '~bootstrap-vue': path.resolve(__dirname, 'node_modules/bootstrap-vue'),
        'bootstrap-vue': path.resolve(__dirname, 'node_modules/bootstrap-vue'),
        // Generic tilde alias
        '~': path.resolve(__dirname, 'node_modules'),
      },
    },

    css: {
      devSourcemap: !isProduction,
      preprocessorOptions: {
        scss: {
          api: 'modern',
          loadPaths: [path.resolve(__dirname, 'node_modules')],
        },
      },
    },

    plugins: [
      inject({
        $: 'jquery',
        jQuery: 'jquery',
      }),

      vue(),

      vueDevTools(),

      VitePWA({
        registerType: 'autoUpdate',
        workbox: {
          clientsClaim: true,
          skipWaiting: true,
          maximumFileSizeToCacheInBytes: 10000000,
        },
      }),

      Copy({
        targets: [
          {
            src: paths.images,
            dest: `${paths.build}/images`,
            rename: (_name, _extension, fullpath) => {
              const keptParts = fullpath.split(path.sep).slice(3);
              return path.join(...keptParts);
            },
          },
          {
            src: paths.fonts,
            dest: `${paths.build}/fonts`,
          },
        ],
        hook: 'writeBundle',
      }),

      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/svg-country-flags/png100px/*',
            dest: 'images/public/node_modules/svg-country-flags/png100px',
          },
        ],
      }),

      // HTML plugin for potential entry points
      createHtmlPlugin({
        minify: isProduction,
        inject: { data: { title: 'ABUS Portal' } },
      }),
    ],

    // Dev server configuration
    server: {
      port: 8080,
      strictPort: true,
      hmr: true,
    },

    // Optimization
    optimizeDeps: {
      include: ['jquery', 'vue', 'lodash', 'bootstrap', 'axios', 'vue-i18n'],
      esbuildOptions: {
        define: { global: 'window' },
      },
    },
  };

  return config;
});
