<template>
  <div class="row">
    <div class="label col-sm-3" v-html="t(label)"></div>

    <div class="list col-sm-4">
      <input
        v-model="value"
        class="form-control"
        type="text"
        :placeholder="t(placeholder)"
        @keyup="setValue({ inode: props.inode, category: props.category, setting: props.setting, value: value })" />
    </div>

    <div class="description col-sm-5"><span v-html="t(description)"></span></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import { useBoxStore } from '@assets/protected/vue/stores';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const boxStore = useBoxStore();

interface Props {
  label: string;
  description: string;
  placeholder: string;
  inode: number;
  category: string;
  setting: string;
}

const props = defineProps<Props>();

const getValue = computed(() => boxStore.getValue);
const setValue = (payload: any) => boxStore.setValue(payload);

const value = ref<string>('');

onMounted(() => {
  value.value = getValue.value(props.inode, props.category, props.setting);
});
</script>

<style lang="scss" scoped>
.row {
  margin-bottom: 20px;

  .label {
    margin-top: 5px;
    font-weight: bold;
    line-height: 30px;
  }

  .description {
    display: table;
    min-height: 40px;

    span {
      display: table-cell;
      vertical-align: middle;
      font-size: 0.8rem;
    }
  }
}
</style>
