<div class="col-md-12">
    <div class="inner">
        <div ng-if="reference.type == 'isReferencedBy'" >{{ "This sales information was"|trans({}, 'sales') }}&nbsp;</div>
        <div ng-if="reference.type == 'hasReferenceOn'">{{ "This sales information"|trans({}, 'sales') }}&nbsp;</div>

        <div class="whatisitdoing" ng-if="reference.replaces && reference.type == 'isReferencedBy'">{{ "replaced by"|trans({}, 'sales_common') }}&nbsp;</div>
        <div class="whatisitdoing" ng-if="reference.replaces && reference.type == 'hasReferenceOn'">{{ "replaces"|trans({}, 'sales_common') }}&nbsp;</div>
        <div class="whatisitdoing" ng-if="!reference.replaces && reference.type == 'isReferencedBy'">{{ "complemented by"|trans({}, 'sales_common') }}&nbsp;</div>
        <div class="whatisitdoing" ng-if="!reference.replaces && reference.type == 'hasReferenceOn'">{{ "complements"|trans({}, 'sales_common') }}&nbsp;</div>

        <a href="/{{ module }}/view/{% verbatim %}{{ reference.number }}{% endverbatim %}">
            <div ng-bind-html="reference.number + ' ' + reference.title"></div>
            <div ng-if="reference.reason && reference.reason != reference.title" ng-bind-html="': ' + reference.reason"></div>
        </a>
    </div>
</div>