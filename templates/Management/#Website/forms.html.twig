{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "Forms"|trans({}, 'management_website') }} | ABUS Kransysteme GmbH{% endblock %}

{% block javascripts %}
    {{ parent() }}

    {# In den Entwicklungsumgebungen werden die einzelnen, nicht verklein<PERSON>en, <PERSON><PERSON> geladen, damit man besser Fehler finden kann #}
    {% if env != 'prod' %}
        {% javascripts 'bundles/abusmanagement/js/abus/_*.js' %}
        <script src="{{ asset_url }}"></script>
        {% endjavascripts %}
    {% else %}
        {# Die Produktivumgebung lädt die verkleinerten, zusammengefassten Dateien #}
        <script src="{{ asset('dist/js/managementBundle_abus.min.js') }}"></script>
    {% endif %}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}

    {# In den Entwicklungsumgebungen werden die einzelnen, nicht verklein<PERSON>, <PERSON><PERSON> gelade<PERSON>, damit man besser Fehler finden kann #}
    {% if env != 'prod' %}
        {% stylesheets 'bundles/abusmanagement/css/abus/_*.css' filter='cssrewrite' %}
        <link rel="stylesheet" href="{{ asset_url }}" />
        {% endstylesheets %}
    {% else %}
        {# Die Produktivumgebung lädt die verkleinerten, zusammengefassten Dateien #}
        <link rel="stylesheet" href="{{ asset('dist/css/abusmanagementBundle_abus.min.css') }}" />
    {% endif %}
{% endblock %}

{% block headline %}{{ "Forms"|trans({}, 'management_website') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}


{% block administration %}
    {{ parent() }}
{% endblock %}


{% block content %}

    <div ng-controller="websiteController" id="website" class="forms">

        {% set blockContent %}

            {% for configuration in configurations %}

                {% if configuration.locale|lower == 'de_de' %}

                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md-11">{{ configuration.configuration.title }}</div>
                            <div class="col-md-1" style="text-align: right;"><button type="button" class="btn btn-sm btn-primary" ng-click="edit({{ loop.index }})"><i class="fa fa-pencil" aria-hidden="true"></i></button></div>
                        </div>

                        <form class="mt10 mb10" ng-show="openForEdit == {{ loop.index }};">
                            <div class="row">
                                <div class="col-md-2">{{ "Title"|trans({}, 'management_website') }}</div>
                                <div class="col-md-10"><input type="text" class="form-control" id="title" ng-model="data[{{ configuration.id }}].title" ng-value="{{ configuration.configuration.title }}"></div>
                            </div>

                            <div class="row">
                                <div class="col-md-12 mt10" style="text-align: right;">
                                    <button type="button" class="btn btn-success" ng-click="store({{ configuration.id }})">
                                        <i class="fa fa-pencil" aria-hidden="true"></i> {{ "Store"|trans({}, 'management_website') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    {#{{ dump(configuration) }}#}

                {% endif %}
            {% endfor %}


        {% endset %}

        {% include 'Frame/TwigTemplates/portlet.html.twig' with {
            'id': false,
            'class': 'primary bgABUSBlue hidden-xs mt20',
            'heading': true,
            'iconClass': '',
            'topic': 'ABUS Kransysteme GmbH',
            'content': blockContent,
            'footer': false,
            'iconRefresh': false,
            'iconToggle': true,
            'iconClose': false,
            'plain': false,
            'movable': false,
            'closedByDefault': false,
            'headingClickable': true
        } only %}
    </div>

{% endblock %}

{% block custom_javascripts %}{% endblock %}

{% block footer %}{% endblock %}
