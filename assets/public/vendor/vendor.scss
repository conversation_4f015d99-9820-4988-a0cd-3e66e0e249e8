@import '@assets/scss/abstract/variables';

//Bootstrap
//$icon-font-path: "~bootstrap-sass/assets/fonts/bootstrap/";
$fa-font-path: "font-awesome/fonts";

@import 'font-awesome/scss/font-awesome.scss';

//Twitter Bootstrap: remove the blue glow in the form inputs
input[type="text"], textarea, select, button:focus {
  outline: none;
  box-shadow: none !important;
}

// Customizing Checkboxes - Bootstrap v5
.form-check-input[type="checkbox"] {
  outline: none;
  box-shadow: none !important;
  background-color: #ffffff !important;
  border: 1px solid #cacaca;
  width: 1.225rem !important;
  height: 1.225rem !important;

  &:checked {
    background-color: #ffffff !important;
    border: 1px solid #cacaca;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='black' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E") !important;
  }

  &:disabled {
    background-color: #eeeeee !important;
    border-color: #e2e2e2 !important;
  }
}

.form-check-label {
  margin: 0 0 0 5px;

  &:hover {
    cursor: pointer;
  }

  .badge {
    position: relative;
    top: -1px;
  }
}

.form-check-input:disabled ~ .form-check-label {
  color: #333333;
}

// Customizing Radios - Bootstrap v5
.form-check-input[type="radio"] {
  outline: none;
  box-shadow: none !important;
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 1px solid #cacaca;
  width: 1.2rem !important;
  height: 1.2rem !important;

  &:checked {
    background-color: #ffffff !important;
    border: 1px solid #cacaca;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23000'/%3E%3C/svg%3E") !important;
  }

  &:disabled {
    background-color: #eeeeee !important;
    border-color: #e2e2e2 !important;
  }
}

.form-check-input[type="radio"] ~ .form-check-label {
  margin: 4px 0 0 5px;
}

.form-check-input[type="radio"]:disabled ~ .form-check-label {
  color: #333333;
}

@media (max-width: 1024px) {
	.right-sidebar-page {
		margin-right: 0;
	}
}
