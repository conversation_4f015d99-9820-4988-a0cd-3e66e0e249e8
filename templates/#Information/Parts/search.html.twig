<div id="search">
    <div>
        <div class="search container-fluid" ng-cloak>
            <div class="row searchBox">

                <div class="form-group col-md-7 col-xs-12 searchField">
                    <input name="search" id="search" type="text" ng-hide="extendedSearchVisible" trigger-typeahead ng-click="searchClick($event, search)" ng-class="{'loading':searching}" typeahead-wait-ms="400" placeholder="{{ "What are you looking for?"|trans({}, 'information') }}" ng-model="searchStr" uib-typeahead="title as title.title for title in getSearch($viewValue)" typeahead-popup-template-url="/information/directive/search" typeahead-loading="searching" typeahead-on-select="onSelect($item, $model, $label)" typeahead-focus-first="true" typeahead-min-length="1" autocomplete="off" class="form-control">
                    <input name="search" id="search" type="text" ng-show="extendedSearchVisible" ng-change="getSearchExtended(true)" ng-model-options="{ debounce: 400 }" placeholder="{{ "What are you looking for?"|trans({}, 'information') }}" ng-model="searchStr" autocomplete="off" class="form-control">
                </div>

                <div class="col-md-offset-3 col-md-2" style="text-align: right;" ng-cloak>
                    <button id="extendedSearch" class="btn btn-primary" ng-click="extendedSearch()" ng-hide="extendedSearchVisible">{{ "Show extended search"|trans({}, 'information') }}</button>
                    <button id="extendedSearch" class="btn btn-danger" ng-click="extendedSearch()" ng-show="extendedSearchVisible"><span class="glyphicon glyphicon-remove" aria-hidden="true" style="top: 2px !important;"></span> {{ "Close extended search"|trans({}, 'information') }}</button>
                </div>

            </div>

            <div class="row" ng-show="extendedSearchVisible" ng-cloak>

                <div class="col-md-12 mb10">
                    <b>{{ "Limit your search to the following categories"|trans({}, 'information') }}:</b>
                </div>

                {% set translatedCategories = [] %}
                {% for category in categories %}
                    {% set translatedCategories = translatedCategories|merge([category|trans({}, 'sales')]) %}
                {% endfor %}

                {% for category in translatedCategories|sort %}
                    {% set translation = '' %}
                    {% for categoryOrg in categories %}
                        {% if categoryOrg|trans({}, 'sales') == category %}
                            {% set translation = categoryOrg %}
                        {% endif %}
                    {% endfor %}

                    <div class="col-md-3">
                        <div class="checkbox-custom" ng-click="getSearchExtended(true)">
                            <input type="checkbox" value="{{ translation }}" id="{{ category }}" ng-model="category['{{ translation }}']">
                            <label for="{{ category }}">{{ category }}</label>
                        </div>
                    </div>

                {% endfor %}
            </div>

            <div id="dateSelection" class="row" ng-show="extendedSearchVisible" ng-cloak>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="">{{ "Published between"|trans({}, 'information') }}</label>
                    <div class="col-md-4">
                        <div class="input-daterange input-group">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            <input type="text" class="form-control" name="start" ng-model="dateStart" ng-change="getSearchExtended(true)" data-date-end-date="0d">
                            <span class="input-group-addon">{{ "and"|trans({}, 'information') }}</span>
                            <input type="text" class="form-control" name="end" ng-model="dateEnd"  ng-change="getSearchExtended(true)" data-date-end-date="0d">
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>