<template>
  <div class="row">
    <div class="label col-sm-3" v-html="t(label)"></div>

    <div class="list col-sm-4">
      <div v-for="language in languages" v-if="singleSelection" :key="language" class="languageContainer">
        <div class="custom-control custom-control-inline custom-radio">
          <input
            :id="inode + category + setting + language"
            v-model="value"
            class="custom-control-input"
            type="radio"
            :value="language"
            :name="inode + category + setting" />
          <label class="custom-control-label" :for="inode + category + setting + language">
            <img :src="getLanguageFlagPath(language)" />
          </label>
        </div>
      </div>

      <div v-for="language in languages" v-if="!singleSelection" :key="language" class="languageContainer">
        <div class="custom-control custom-checkbox">
          <input
            :id="inode + category + setting + language"
            v-model="value"
            class="custom-control-input"
            type="checkbox"
            :value="language" />
          <label class="custom-control-label" :for="inode + category + setting + language">
            <img :src="getLanguageFlagPath(language)" />
          </label>
        </div>
      </div>
    </div>

    <div class="description col-sm-5" v-html="t(description)"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import { useBoxStore } from '@assets/protected/vue/stores';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const boxStore = useBoxStore();

const { getLanguageFlagPath } = useLanguageHelpers();

interface Props {
  label: string;
  description: string;
  singleSelection?: boolean;
  inode: number;
  category: string;
  setting: string;
}

const props = withDefaults(defineProps<Props>(), {
  singleSelection: false,
});

const getValue = computed(() => boxStore.getValue);
const setValue = (payload: any) => boxStore.setValue(payload);

const value = ref<any>([]);
const languages = ref<Array<string>>(['DE', 'EN', 'FR', 'ES']);

onMounted(() => {
  value.value = getValue.value(props.inode, props.category, props.setting);
  if (props.singleSelection === false && value.value === null) value.value = [];
  if (props.singleSelection === true && value.value === null) value.value = '';
});
</script>

<style lang="scss" scoped>
.row {
  .label {
    font-weight: bold;
    line-height: 30px;
  }

  div.languageContainer {
    float: left;
    margin-right: 30px;

    .custom-control-inline {
      margin-right: 0;
    }

    img {
      position: relative;
      top: -2px;
      width: 25px;
    }
  }

  .description {
    font-size: 0.8rem;
  }
}
</style>
