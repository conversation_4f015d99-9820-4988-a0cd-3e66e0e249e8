<template>
  <div v-cloak id="reportProblemController">
    <div id="page-header" class="clearfix mb-0">
      <div class="page-header">
        <h2 v-html="$t('frame/reportProblem/title')"></h2>
        <span v-html="$t('frame/reportProblem/headline')"></span>
      </div>
    </div>

    <div v-show="thankYou" class="bs-callout bs-callout-success mt20">
      <h4 v-html="$t('frame/reportProblem/successfullyTransferred')"></h4>
      <p v-html="$t('frame/reportProblem/thankYou')"></p>
    </div>

    <a
      v-show="thankYou"
      class="btn btn-abus-blue-light mr5 mb10"
      type="button"
      href="/reportProblem"
      v-html="$t('frame/reportProblem/report_another')"></a>

    <form v-show="!thankYou" class="mt20">
      <div class="form-group row" :class="{ 'bs-callout bs-callout-danger error': error.type }">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/typeOfProblem')"></label>
        <div class="col-lg-4 col-md-9">
          <select v-model="type" class="form-control">
            <option value="0" v-html="$t('frame/reportProblem/pleaseChoose')"></option>
            <option value="bug" v-html="$t('frame/reportProblem/bug')"></option>
            <option value="improvement" v-html="$t('frame/reportProblem/improvement')"></option>
            <option value="suggestion" v-html="$t('frame/reportProblem/suggestion')"></option>
          </select>
          <p v-show="error.type" class="error" v-html="$t('frame/reportProblem/chooseType')"></p>
        </div>
      </div>

      <div class="form-group row" :class="{ 'bs-callout bs-callout-danger error': error.service }">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/affectedService')"></label>
        <div class="col-lg-4 col-md-9">
          <select v-model="service" class="form-control">
            <option value="0" v-html="$t('frame/reportProblem/pleaseChoose')"></option>

            <option v-for="module in internalModules" :value="module.value" v-html="module.name"></option>

            <optgroup v-for="moduleGroup in internalModuleGroups" :label="moduleGroup.name">
              <option v-for="module in moduleGroup.groups" :value="module.value" v-html="module.name"></option>
            </optgroup>
          </select>
          <p v-show="error.service" class="error" v-html="$t('frame/reportProblem/serviceError')"></p>
        </div>
      </div>

      <div class="form-group row" :class="{ 'bs-callout bs-callout-danger error': error.description }">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/description')"></label>
        <div class="col-lg-10 col-md-9">
          <Editor id="reportProblemEditor" v-model="description" preset="basic" />
          <p v-show="error.description" class="error" v-html="$t('frame/reportProblem/descriptionError')"></p>
          <div class="alert alert-danger mt15">
            <div class="exclamation-square">
              <font-awesome-icon
                class="fa-2x"
                aria-hidden="true"
                :icon="['fas', 'exclamation-square']"></font-awesome-icon>
            </div>
            <div>
              {{ $t('frame/reportProblem/noImages') }}<br />
              {{ $t('frame/reportProblem/noImages2') }}
            </div>
          </div>
        </div>
      </div>

      <div class="form-group row">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/images')"></label>
        <div class="col-lg-4 col-md-9">
          <dropzone :uploader="uploader" :multiple="true">
            <div class="headlineImage">
              <font-awesome-icon
                class="fa-inverse fa-4x"
                aria-hidden="true"
                :icon="['far', 'cloud-upload']"></font-awesome-icon>
            </div>
            <div class="headline">
              {{ $t('frame/reportProblem/dropzone') }}
            </div>
            <div class="extensions">(.jpg, .jpeg, .png, .gif)</div>
            <div class="size" v-html="$t('frame/reportProblem/maxSize') + ' 5 MB'"></div>
          </dropzone>
        </div>
        <div class="col-lg-6 col-md-9 images">
          <a v-for="image in imageLightbox" data-lightbox="images" :href="image">
            <img :src="image" />
          </a>
        </div>
      </div>

      <div class="row setup">
        <div class="col-lg-12" style="font-style: italic" v-html="$t('frame/reportProblem/setup')"></div>
      </div>

      <div class="form-group row">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/os')"></label>
        <div class="col-lg-4 col-md-9">
          <input v-model="os" class="form-control" name="browser" type="text" />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/browser')"></label>
        <div class="col-lg-4 col-md-9">
          <input v-model="browser" class="form-control" name="browser" type="text" />
        </div>
      </div>

      <div class="form-group row">
        <label class="col-lg-2 col-md-3 control-label" v-html="$t('frame/reportProblem/browserVersion')"></label>
        <div class="col-lg-4 col-md-9">
          <input v-model="browserVersion" class="form-control" name="browser" type="text" />
        </div>
      </div>

      <div class="col-lg-12">
        <button
          class="btn btn-success"
          type="submit"
          @click="sendProblem"
          v-html="$t('frame/reportProblem/send')"></button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import Editor from '@assets/protected/vue/component/helper/Editor.vue';
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// TODO: Replace with Vueuse dropzone: https://vueuse.org/core/useDropZone/
//import Dropzone from 'vue-fineuploader/dropzone.vue';
//import FineUploaderTraditional from 'fine-uploader-wrappers';

import { useModalStore } from '@assets/protected/vue/stores';
import { useScroll } from '@assets/protected/vue/composables/useScroll';
import { useDeviceDetection } from '@assets/protected/vue/composables/useDeviceDetection';

import 'lightbox2/dist/js/lightbox';
import 'lightbox2/dist/css/lightbox.css';

const deviceInfo = useDeviceDetection();
const scroll = useScroll();
const modalStore = useModalStore();

const props = defineProps<{
  uploadUrl: string;
  imageUploadUrl: string;
  moduleGroups: string;
  modules: string;
  domain: string;
}>();

const internalModuleGroups = ref<any>(null);
const internalModules = ref<any>(null);

const images = reactive<Record<string, any>>({});
const imageLightbox = ref<string[]>([]);

const os = ref<string>(deviceInfo.os);
const browser = ref<string>(deviceInfo.browser);
const browserVersion = ref<string>(deviceInfo.browserVersion);
const type = ref<string>('0');
const service = ref<string>('0');
const description = ref<string>('');
const error = reactive({
  type: false,
  service: false,
  description: false,
});

const thankYou = ref<boolean>(false);

watch(type, (value: string) => {
  error.type = value === '';
});

watch(service, (value: string) => {
  error.service = value === '';
});

watch(description, (value: string) => {
  error.description = value === '' || value === '<p><br></p>';
});

const uploader = new FineUploaderTraditional({
  options: {
    request: {
      endpoint: props.imageUploadUrl,
    },
    callbacks: {
      onComplete: (id: any, name: any, response: any) => {
        if (response.success === true) {
          addAttachment(response);
        } else {
          modalStore.showModal({ cssClass: 'error', message: response.message });
        }
      },
    },
  },
});

/**
 * Fügt einen hochgeladenen Anhang dem AttachmentsArray hinzu
 *
 * @param response
 */
const addAttachment = (response: any): void => {
  images[response.file.name] = response.file;
  description.value += '<img alt="" src="' + props.domain + '/reportProblem/image/' + response.file.name + '">';
  imageLightbox.value.push(props.domain + '/reportProblem/image/' + response.file.name);
};

const checkErrors = (): boolean => {
  return error.type || error.service || error.description;
};

const sendProblem = (event: Event): void => {
  event.preventDefault();

  error.type = type.value == '0'; // == ist hier wichtig
  error.service = service.value == '0'; // == ist hier wichtig
  error.description = description.value === '';

  // Nur weitermachen wenn es keine Fehler gibt
  if (true === checkErrors()) return;

  const params = {
    type: type.value,
    service: service.value,
    description: description.value,
    os: os.value,
    browser: browser.value,
    browserVersion: browserVersion.value,
  };

  axios
    .post(props.uploadUrl, new URLSearchParams(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
    .then(() => {
      thankYou.value = true;
      scroll.scrollToSelector('#page-header');
    })
    .catch((error: any) => {
      modalStore.showModal({
        cssClass: 'error',
        title: 'An error has occured',
        message: error?.data?.message,
      });
    });
};

onMounted(() => {
  internalModuleGroups.value = JSON.parse(props.moduleGroups);
  internalModules.value = JSON.parse(props.modules);
});
</script>

<style lang="scss" scoped>
@import '@assets/scss/abstract/variables';

.exclamation-square {
  float: left;
  padding-right: 20px;
}

.fine-uploader-dropzone-container {
  background-color: $abus-yellow-dark;
  width: 100%;
  height: 85px;
  font-size: 18px;
  color: #ffffff;

  text-align: center;
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
  border: 1px solid darken($abus-yellow-dark, 5%);

  -webkit-box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);
  box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);

  .headlineImage {
    float: left;
    padding-top: 5px;
    padding-left: 20px;
  }

  .headline {
    margin-top: 5px;
    line-height: 30px;
  }

  .extensions,
  .size {
    font-size: 13px;
    line-height: 23px;
  }

  &.vue-fine-uploader-dropzone-active {
    cursor: pointer;
    background-color: #0a98bb;
    border: 1px solid #076d86;
  }
}

.images {
  img {
    height: 90px;
    padding-right: 20px;

    &:hover {
      cursor: pointer;
    }
  }
}

button,
a.btn {
  float: right;
}

.error {
  display: flex;
}

p.error {
  font-weight: bold;
  color: #db5565;
  margin: 10px 0 0 0;
}

.setup {
  margin-top: 40px;
  margin-bottom: 20px;
  font-weight: bold;
}
</style>
