<div class="row">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h4 class="panel-title">{{ 'Publishing'|trans({}, 'information') }}</h4>
        </div>
        <div class="panel-body" style="display: block;">

            {#<div class="row">#}
                {#<div class="col-md-4">#}
                    {#<div class="checkboxContainer blue pl10" ng-class="{active: data.publishing.draft}">#}
                        {#<div class="checkbox-custom" style="height: 34px;">#}
                            {#<input type="checkbox" name="draft" value="true" id="draft" ng-model="data.publishing.draft" ng-change="save('draft')">#}
                            {#<label for="draft" ng-class="{'stored': visualizeStore['draft']}" style="line-height: 34px;">{{ 'Store as draft'|trans({}, 'information') }}</label>#}
                        {#</div>#}
                    {#</div>#}
                {#</div>#}
            {#</div>#}

            <div class="row">
                <div class="col-md-4">
                    <div class="input-group" title="{{ 'Will not be published before this date'|trans({}, 'information') }}">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input id="basic-datepicker" type="text" class="form-control" ng-model="data.publishing.releasedate" ng-blur="save('releasedate')" ng-class="{'stored': visualizeStore['releasedate']}">
                    </div>
                </div>
            </div>

            <div class="row mb0">
                <div class="col-md-4">
                    <div class="checkboxContainer blue pl10" ng-class="{active: data.publishing.email}">
                        <div class="checkbox-custom" style="height: 34px;">
                            <input type="checkbox" name="email" value="true" id="email" ng-model="data.publishing.email" ng-change="save('email')">
                            <label for="email" ng-class="{'stored': visualizeStore['email']}" style="line-height: 34px;">{{ 'Send eMail to receivers on publishing'|trans({}, 'information') }}</label>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
