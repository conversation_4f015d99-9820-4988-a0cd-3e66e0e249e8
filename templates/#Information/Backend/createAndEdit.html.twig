{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/information/information.js') }}"></script>

    {#<script type="text/javascript">#}
        {#$(document).ready(function() {#}

            {#$('#basic-datepicker').datepicker({#}
                {#language: "{{ getLanguage(true, true) }}",#}
                {#todayHighlight: true,#}
                {#autoclose: true#}
            {#});#}

        {#});#}
    {#</script>#}

{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/information/information.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block ng_init_body %}id="information" ng-controller="SingleInformationBackendController as ctrl" ng-init="data.section='{{ module }}'; data.number={% if number is defined %}{{ number }}{% else %}false{% endif %}; data.language = '{{ getLanguage(true, true) }}'; init();"{% endblock %}

{% block administration %}
    {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' %}
        {% block administration %}
            {{ parent() }}

            <div class="sidebar-panel">
                <h5 class="sidebar-panel-title">
                    {{ ["apis/", module, "/headline_short"]|join|trans({}, 'across') }}
                </h5>
            </div>

            <div class="content">
                <a href="{{ path('abus_information_administration', {'section': module, 'domain': getDomain(), 'subdomain': getSubdomain('portal') }) }}" class="btn btn-primary btn-block mr5">{{ "Administration"|trans({}, 'information') }}</a>
                <hr>
                <button ng-show="data.workflow == 'draft'" ng-click="translateAndPublish()" class="btn btn-success btn-block mr5" style="white-space: normal">{{ "I'm ready,"|trans({}, 'information') }}<br />{{ "please translate and publish"|trans({}, 'information') }}</button>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block headline %}{{ "_headline"|trans({}, module) }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div>

        {# Loading Indicator #}
        <div id="loading" ng-show="loading"></div>

        <div id="errors" class="alert alert-danger mt20" ng-show="errors.length && !loading" ng-cloak>
            <li ng-repeat="error in errors" ng-bind-html="error"></li>
        </div>

        <div id="informationBackend" ng-hide="loading" ng-cloak>

            {% include 'Information/Parts/chooseNumber.html.twig' %}

            <div class="container-fluid" ng-if="data.number">

                <div>
                    <table class="upload">
                        <tr ng-repeat="file in files">
                            <td ng-bind-html="file.fileName"></td>
                            <td>
                                <select class="form-control">
                                    <option>{{ "Embedded image"|trans({}, 'information') }}</option>
                                    <option>{{ "Attachment used for all languages"|trans({}, 'information') }}</option>
                                    <option>{{ "Attachment used for english only"|trans({}, 'information') }}</option>
                                    <option>{{ "Attachment used for german only"|trans({}, 'information') }}</option>
                                    <option>{{ "Attachment used for french only"|trans({}, 'information') }}</option>
                                    <option>{{ "Attachment used for spanish only"|trans({}, 'information') }}</option>
                                </select>
                            </td>
                            <td>
                                <i class="fa fa-trash-o" aria-hidden="true" title="{{ "Remove"|trans({}, 'information') }}"></i>
                            </td>
                            <td>
                                <i class="fa fa-picture-o" aria-hidden="true" title="{{ "Reuse image"|trans({}, 'information') }}"></i>
                            </td>
                        </tr>

                    </table>
                </div>

                {% include 'Information/Parts/createLanguageBlock.html.twig' with { 'language' : 'german' } %}
                {% include 'Information/Parts/createLanguageBlock.html.twig' with { 'language' : 'english' } %}
                {% include 'Information/Parts/createLanguageBlock.html.twig' with { 'language' : 'french' } %}
                {% include 'Information/Parts/createLanguageBlock.html.twig' with { 'language' : 'spanish' } %}

                {% include 'Information/Parts/createReferenceBlock.html.twig' %}

                {% include 'Information/Parts/createReceiversBlock.html.twig' %}

                {% include 'Information/Parts/createCategoriesBlock.html.twig' %}

                {% include 'Information/Parts/createAuthorBlock.html.twig' %}

                {% include 'Information/Parts/createPublishingBlock.html.twig' %}

                {% include 'Information/Parts/createInternalMemoBlock.html.twig' %}

                <div class="row">
                    <div class="col-md-push-7 col-md-2">
                        <button class="btn btn-warning btn-lg" style="width: 100%;">
                            <i class="far fa-hdd" aria-hidden="true"></i>
                            &nbsp;&nbsp;
                            {{ "Store"|trans({}, 'information') }}
                        </button>
                    </div>

                    <div class="col-md-push-8 col-md-2">
                        <button class="btn btn-success btn-lg" style="width: 100%;">
                            <i class="fa fa-sign-out" aria-hidden="true"></i>
                            &nbsp;&nbsp;
                            {{ "Store & close"|trans({}, 'information') }}
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

{% endblock %}

{% block modalController %}
    ng-controller="SingleInformationBackendController"
{% endblock %}


{% block custom_javascripts %}{% endblock %}
