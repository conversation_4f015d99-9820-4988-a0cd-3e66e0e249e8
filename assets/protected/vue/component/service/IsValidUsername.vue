<template>
  <input
    class="form-control text-white"
    name="username"
    type="text"
    :class="isValid ? 'bg-success' : 'bg-danger'"
    v-model.lazy="usernameWithCompanyNumber"
    placeholder="AD-Benutzername" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, defineProps } from 'vue';
import axios from 'axios';
import { useServicesStore } from '@assets/protected/vue/stores';

interface Props {
  name?: string;
  path: string;
}

const props = defineProps<Props>();

const servicesStore = useServicesStore();

const username = ref<string>('');
const isValid = ref<boolean>(false);
const usernameWithCompanyNumber = ref<string>('');

watch(usernameWithCompanyNumber, async (val) => {
  if (!props.path || !val) {
    isValid.value = false;
    return;
  }
  try {
    const { data } = await axios.get<boolean>(`${props.path}/${val}`);
    isValid.value = data;
  } catch {
    isValid.value = false;
  }
});

let unwatchStore: null | (() => void) = null;

onMounted(() => {
  username.value = props.name ?? '';
  usernameWithCompanyNumber.value = username.value;

  // Watch for company number changes
  watch(
    () => servicesStore.getCompanyNumber,
    (companyNumber: string) => {
      usernameWithCompanyNumber.value = `${username.value}${companyNumber}`;
    },
    { immediate: true }
  );
});

onUnmounted(() => {
  if (unwatchStore) {
    unwatchStore();
    unwatchStore = null;
  }
});
</script>
