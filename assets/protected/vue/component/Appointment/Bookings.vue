<template>
  <div id="bookingsTool">
    <Spinner v-show="loading" size="big" message="loading..." />

    <div v-if="show && !loading">
      <h4 v-show="bookings.length > 0">
        <strong v-html="category.niceName + ' - ' + event.name"></strong>
      </h4>

      <sort-table :items="bookings" :fields="fieldsMap" :striped="true" :hover="true">
        <template #cell(aktionen)="row">
          <b-button
            v-if="row.item.Status === 'Offen'"
            class="me-1"
            size="sm"
            @click="cancelItem(row.item.Aktionen)"
            variant="info">
            Stornieren
          </b-button>
          <b-button class="me-1" size="sm" @click="deleteItem(row.item.Aktionen)" variant="danger"> Löschen </b-button>
        </template>
      </sort-table>
    </div>

    <div v-if="!show && !error" class="bs-callout bs-callout-info">
      Bitte eine Kategorie und Veranstaltung auswählen und dann den Button "Buchungen anzeigen" klicken.
    </div>

    <div v-if="error" class="bs-callout bs-callout-danger" v-html="error"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';
import SortTable from '@assets/protected/vue/component/helper/SortTable.vue';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import { useBookingsStore } from '@assets/protected/vue/stores/useBookingsStore';

const { proxy } = getCurrentInstance()!;

const bookingsStore = useBookingsStore();

const bookings = computed<Array<Record<string, unknown>>>(() => bookingsStore.getBookings);
const show = computed<boolean>(() => bookingsStore.getShowTable);
const category = computed<Record<string, any>>(() => bookingsStore.getCategory);
const event = computed<Record<string, any>>(() => bookingsStore.getEvent);
const loading = computed<boolean>(() => bookingsStore.getLoading);
const error = computed<string>(() => bookingsStore.getError);

function cancel(id: number) {
  bookingsStore.cancelBooking(id);
}

function cancelItem(id: number) {
  // BootstrapVue modal via proxy for Vue 2.7/3 compatibility
  proxy?.$bvModal
    ?.msgBoxConfirm('Möchten Sie wirklich stornieren ?', {
      title: 'Stornieren',
      size: 'sm',
      buttonSize: 'sm',
      okVariant: 'danger',
      okTitle: 'Ja',
      cancelTitle: 'Nein',
      footerClass: 'p-2',
      hideHeaderClose: false,
      centered: true,
    })
    .then((value: boolean) => {
      if (value) {
        bookingsStore.cancelBooking(id);
      }
    })
    .catch(() => {});
}

function deleteItem(id: number) {
  proxy?.$bvModal
    ?.msgBoxConfirm('Möchten Sie wirklich löschen ?', {
      title: 'Löschen',
      size: 'sm',
      buttonSize: 'sm',
      okVariant: 'danger',
      okTitle: 'Ja',
      cancelTitle: 'Nein',
      footerClass: 'p-2',
      hideHeaderClose: false,
      centered: true,
    })
    .then((value: boolean) => {
      if (value) {
        bookingsStore.deleteBooking(id);
      }
    })
    .catch(() => {});
}

const fieldsMap = computed(() => {
  const keys: Array<{ key: string; sortable: boolean }> = [];
  const list = bookings.value;
  if (Array.isArray(list) && list.length > 0) {
    Object.keys(list[0] as Record<string, unknown>).forEach((key) => {
      keys.push({ key, sortable: true });
    });
  }
  return keys;
});
</script>
