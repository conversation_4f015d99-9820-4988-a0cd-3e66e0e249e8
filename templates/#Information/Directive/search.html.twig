{% verbatim %}
    <ul class="dropdown-menu" ng-show="isOpen() && !moveInProgress" ng-style="{top: position().top+'px', left: position().left+'px'}" style="display: block;" role="listbox" aria-hidden="{{!isOpen()}}">

        <li ng-if="matches[0].model.foundRows > 20" class="searchHintMax"><span style="white-space: nowrap;">{% endverbatim %}{{ "Due to the high number of"|trans({}, 'information') }} {% verbatim %}{{ matches[0].model.foundRows }}{% endverbatim %} {{ "results, the total number of results was restricted to 20."|trans({}, 'information') }}</span><br>{{ "Please refine your search or use the extended search."|trans({}, 'information') }}{% verbatim %}</li>

        <li ng-if="matches[0].model.foundRows == 0" class="searchHintNone">{% endverbatim %}{{ "No search results found."|trans({}, 'information') }}{% verbatim %}</li>

        <li ng-if="$index != 0" ng-init="selectActive(1)" ng-repeat="match in matches track by $index" ng-class="{active: isActive($index) }" ng-mouseenter="selectActive($index)" ng-click="selectMatch($index)" role="option" id="{{::match.id}}">
            <a href tabindex="-1" ng-bind-html="match.model.archived + (match.label | uibTypeaheadHighlight:query) + match.model.flags"></a>
        </li>

    </ul>
{% endverbatim %}