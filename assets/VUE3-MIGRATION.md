# Migration from Vue 2 to Vue 3

## Vue 2 to Vue 3 Migration Guide
https://v3-migration.vuejs.org/

## Roadmap
- [x] Update Vue to 2.7 
- [x] Updating all vue components to use Composition API
  - Updated components are written in PascalCase.
- [x] Update Vue to 3
- [ ] Update or change dependencies (These dependencies should be updated after the Vue update)
  - [ ] Vue dependencies and components
    - [x] Replace Vuex with Pinia.
    - [x] Update `vue-i18n`.
    - [ ] Replace `vue-fineuploader`.
    - [ ] Replace `vue-infinite-scroll` with https://vueuse.org/core/useInfiniteScroll/.
    - [x] Remove `vue-js-toggle-button`.
    - [x] Replace `vue-snotify`.
    - [x] Remove `vue-simple-spinner`.
  - [ ] Bootstrap
    - [ ] Update to version 5.
    - [ ] Update to Bootstrap Vue Next.
    - [ ] Replace `pc-bootstrap4-datetimepicker` and `vue-bootstrap-datetimepicker`.
  - [ ] Update ckeditor.
    - [ ] Change `@ckeditor/ckeditor5-vue2` to `@ckeditor/ckeditor5-vue`.
    - [ ] Remove `vue-ckeditor2`.
  - [x] Remove `moment`. It is not maintained anymore and was replaced by `dayjs`.
  - [ ] Update vue-fontawesome.
  - etc.


## Prompt
When updating vue components to use the Composition API, you can use the following prompt to help you:

```
Migrate only this Vue component to use the Composition API with Vue 2.7. The goal is to prepare for a future upgrade to Vue 3, so refactor the component to be compatible with both versions.

Apply the following changes:
- Use Vue.js documentation from https://vuejs.org/llms.txt
- Use Composition API (Vue 2.7 syntax). Use the <script setup lang="ts"> syntax instead of defineComponent() for Composition API. This ensures cleaner code and aligns with future Vue 3 best practices.
- Ignore i18n — it's already globally available in the app, so omit any related imports or setup.
- If a translation is needed in the script setup, use the t() function and import it like this:
  const { proxy } = getCurrentInstance();
  const t = proxy.$t;
- If locale is defined as a variable, use the global locale instead:
  const locale = proxy.$i18n.locale;
- Never use reactive, always use ref!
- Use ESM syntax — this project has migrated from Webpack Encore to Vite, so use import statements instead of require.
- Use TypeScript where appropriate — convert the component to .vue with <script lang="ts"> if feasible. If you don't know the type of a variable, don't use any instead just omit the type declaration.
- Replace moment.js with dayjs — migrate all date/time logic accordingly.
- Replace vue-simple-spinner — if used, replace it with our internal component: import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
- Clean and modernize — simplify the code where useful, improve readability, and ensure it’s ready for Vue 3 with minimal further changes.
- If the store is needed import the appropriate Pinia store like this: import { useModalStore, useBoxStore, useNewsStore, useServicesStore, useBookingsStore } from '@assets/protected/vue/stores';
- If the library qs is used, solve it with Vanilla JS and native functions.
- If you find any jQuery code replace it with Vanilla JS.
- If you find comments in the code, keep them as they are, in the original language.
- For date formatting use the function formatDate from the composable useDateHelpers.ts. Like this:
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';
const { formatDate } = useDateHelpers();
```
