<template>
  <div class="my-4">
    <h4>
      <strong>{{ title }}</strong>
    </h4>
    <sort-table :items="accounts" :fields="fields" :striped="true" :small="true" :hover="true">
      <!-- header -->
      <template #head(task.doneMA)="data">
        <span id="doneMA">{{ data.label }}</span>
        <b-tooltip target="doneMA" placement="bottom">Marketing</b-tooltip>
      </template>

      <template #head(task.doneEO)="data">
        <span id="doneEO">{{ data.label }}</span>
        <b-tooltip target="doneEO" placement="bottom">Entwicklung-Organistation</b-tooltip>
      </template>

      <template #head(task.doneIT)="data">
        <span id="doneIT">{{ data.label }}</span>
        <b-tooltip target="doneIT" placement="bottom">IT</b-tooltip>
      </template>

      <template #head(task.doneVO)="data">
        <span id="doneVO">{{ data.label }}</span>
        <b-tooltip target="doneVO" placement="bottom">Vertrieb-Organisation</b-tooltip>
      </template>

      <template #head(task.doneOV)="data">
        <span id="doneOV">{{ data.label }}</span>
        <b-tooltip target="doneOV" placement="bottom">OVISS</b-tooltip>
      </template>

      <template #head(task.donePA)="data">
        <span id="donePA">{{ data.label }}</span>
        <b-tooltip target="donePA" placement="bottom">ProALPHA</b-tooltip>
      </template>

      <!-- header -->

      <template #cell(task.oldAccountHolder)="data">
        <span
          v-if="data.item.type === 'create' || data.item.type === 'vpn_reassign' || data.item.type === 'vpn_create'"
          >{{ data.item.task.newAccountHolder }}</span
        >
        <span v-if="data.item.type === 'delete' || data.item.type === 'request'">{{
          data.item.task.oldAccountHolder
        }}</span>
      </template>

      <template #cell(task.company)="data">
        <span>{{ data.item.task.company }}</span>
      </template>

      <template #cell(task.doneMA)="data">
        <svg
          v-if="data.item.allowed === '1'"
          :id="'ma_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-else
          :id="'ma_checked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(task.doneEO)="data">
        <svg
          v-if="data.item.task.doneEO"
          :id="'eo_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-if="data.item.places.includes('EO') && !data.item.task.doneEO && data.item.task.mailEO"
          :id="'eo_checked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(task.doneIT)="data">
        <svg
          v-if="data.item.task.doneIT"
          :id="'it_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-if="data.item.places.includes('IT') && !data.item.task.doneIT && data.item.task.mailIT"
          :id="'it_checked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(task.doneVO)="data">
        <svg
          v-if="data.item.task.doneVO"
          :id="'vo_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-if="data.item.places.includes('VO') && !data.item.task.doneVO && data.item.task.mailVO"
          :id="'vo_checked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(task.doneOV)="data">
        <svg
          v-if="data.item.task.doneOV"
          :id="'ov_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-if="data.item.places.includes('OV') && !data.item.task.doneOV && data.item.task.mailOV"
          :id="'ov_unchecked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(task.donePA)="data">
        <svg
          v-if="data.item.task.donePA"
          :id="'pa_checked' + data.item.id"
          class="bi bi-check-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: green">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z" />
        </svg>
        <svg
          v-if="data.item.places.includes('PA') && !data.item.task.donePA && data.item.task.mailPA"
          :id="'pa_checked' + data.item.id"
          class="bi bi-x-circle"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 16 16"
          style="color: red">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      </template>

      <template #cell(type)="data">
        <span class="font-weight-bold" v-html="$t('account/workflow/type/' + data.item.type)"></span>
      </template>

      <template #cell(lastModified)="data">
        <span v-html="formatDate(data.item.lastModified)"></span>
      </template>

      <template #cell(show)="data">
        <a data-toggle="modal" href="" :data-target="'#account' + data.item.id">
          <svg
            class="bi bi-eye"
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="currentColor"
            viewBox="0 0 16 16">
            <path
              d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
            <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
          </svg>
        </a>
        <!-- Modal -->
        <div
          :id="'account' + data.item.id"
          ref="account"
          class="modal fade"
          role="dialog"
          aria-hidden="true"
          tabindex="-1"
          :aria-labelledby="'#account' + data.item.id">
          <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content bg-dark text-white">
              <div class="modal-header">
                <h5 id="exampleModalCenterTitle" class="modal-title font-weight-bold text-uppercase">Abus workflow</h5>
                <button class="close text-white" data-dismiss="modal" type="button" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <div class="row border-bottom">
                  <div class="col-5 text-start" v-html="$t('account/workflow/action')"></div>
                  <div
                    class="col-7 text-start font-weight-bold"
                    v-html="$t('account/workflow/type/' + data.item.type)"></div>
                </div>

                <div v-if="data.item.task.reason" class="row border-bottom">
                  <div class="col-5 text-start" v-html="$t('account/workflow/reason')"></div>
                  <div class="col-7 text-start font-weight-bold" v-html="data.item.task.reason"></div>
                </div>

                <div v-if="data.item.task.remarkField" class="row border-bottom">
                  <div class="col-5 text-start" v-html="$t('account/workflow/remark')"></div>
                  <div class="col-7 text-start font-weight-bold" v-html="data.item.task.remarkField"></div>
                </div>

                <div class="row border-bottom py-3">
                  <div class="col-5 text-start" v-html="$t('account/workflow/Requested_by')"></div>
                  <div class="col-7 text-start">
                    <div v-html="data.item.manager_company"></div>
                    <div v-html="data.item.manager"></div>
                    <div v-html="data.item.manager_email"></div>
                  </div>
                </div>

                <div v-if="data.item.type === 'delete'">
                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/User')"></div>
                    <div class="col-7" v-html="data.item.task.oldAccountHolder"></div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/AD_Username')"></div>
                    <div class="col-7" v-html="data.item.task.samaccountname"></div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/Company')"></div>
                    <div class="col-7" v-html="data.item.task.company"></div>
                  </div>

                  <div v-if="data.item.task.keepToken === '1'" class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/Token')"></div>
                    <div class="col-7" v-html="$t('account/workflow/token_for_later_usage')"></div>
                  </div>

                  <div v-if="data.item.task.ovissReceiver" class="row py-1 border-top">
                    <div class="col-5 text-start" v-html="$t('account/workflow/OVISS')"></div>
                    <div class="col-7" v-html="data.item.task.ovissReceiver"></div>
                  </div>

                  <div v-if="data.item.task.ovissReceiver" class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/AD_Username')"></div>
                    <div class="col-7" v-html="data.item.task.samaccountname2"></div>
                  </div>
                </div>

                <div v-if="data.item.type === 'create'">
                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/User')"></div>
                    <div class="col-7" v-html="data.item.task.newAccountHolder"></div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/AD_Username')"></div>
                    <div class="col-7" v-html="data.item.task.samaccountname"></div>
                  </div>

                  <div class="row py-1 border-bottom">
                    <div class="col-5 text-start" v-html="$t('account/workflow/Company')"></div>
                    <div class="col-7" v-html="data.item.task.company"></div>
                  </div>

                  <div v-if="data.item.task.serial_number" class="row py-1 border-bottom">
                    <div class="col-5 text-start" v-html="$t('account/workflow/Token_serial_number')"></div>
                    <div class="col-7" v-html="data.item.task.serial_number"></div>
                  </div>
                  <div class="row py-1">
                    <div class="col-5 text-start">{{ $t('account/workflow/Reference_user') }}</div>
                    <div class="col-7 font-weight-bold">{{ data.item.task.referenceAccountHolder }}</div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start">{{ $t('account/workflow/AD_Username') }}</div>
                    <div class="col-7 font-weight-bold">{{ data.item.task.samaccountname2 }}</div>
                  </div>

                  <div v-if="data.item.task.token === '1'" class="row py-1 border-top">
                    <div class="col-5 text-start">{{ $t('account/workflow/Token') }}</div>
                    <div class="col-7 font-weight-bold">{{ $t('Please send him a new token') }}</div>
                  </div>
                </div>

                <div v-if="data.item.type === 'request'">
                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/User')"></div>
                    <div class="col-7" v-html="data.item.task.oldAccountHolder"></div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/AD_Username')"></div>
                    <div class="col-7" v-html="data.item.task.samaccountname"></div>
                  </div>

                  <div class="row py-1">
                    <div class="col-5 text-start" v-html="$t('account/workflow/Company')"></div>
                    <div class="col-7" v-html="data.item.task.company"></div>
                  </div>

                  <div class="row py-1 border-bottom">
                    <div class="col-5 text-start" v-html="$t('account/workflow/reason')"></div>
                    <div class="col-7" v-html="data.item.task.reason"></div>
                  </div>
                </div>

                <div
                  class="row"
                  :class="
                    data.item.task.message_step_1 ||
                    data.item.task.message_step_2 ||
                    data.item.task.message_step_3 ||
                    data.item.task.message_step_4 ||
                    data.item.task.message_step_5 ||
                    data.item.task.message_step_6
                      ? 'border-top'
                      : ''
                  "></div>

                <div v-if="data.item.task.message_step_1" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' Marketing:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_1"></div>
                </div>

                <div v-if="data.item.task.message_step_2" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' Entwicklung-Organistation:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_2"></div>
                </div>

                <div v-if="data.item.task.message_step_3" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' IT:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_3"></div>
                </div>

                <div v-if="data.item.task.message_step_4" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' ProALPHA:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_4"></div>
                </div>

                <div v-if="data.item.task.message_step_5" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' Vertrieb-Organisation:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_5"></div>
                </div>

                <div v-if="data.item.task.message_step_6" class="row py-2">
                  <div class="col-5" v-html="$t('account/workflow/Message') + ' OVISS:'"></div>
                  <div class="col-7" v-html="data.item.task.message_step_6"></div>
                </div>

                <div class="row my-2 border-top"></div>

                <div class="row">
                  <div class="col-5" v-html="$t('account/workflow/Created')"></div>
                  <div class="col-7 font-weight-bold" v-html="formatDate(data.item.created)"></div>
                </div>

                <div class="row py-1">
                  <div class="col-5" v-html="$t('account/workflow/lastmodified')"></div>
                  <div class="col-7 font-weight-bold" v-html="formatDate(data.item.lastModified)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </sort-table>
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue';
import SortTable from '@assets/protected/vue/component/helper/SortTable.vue';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';

const { formatDate } = useDateHelpers();

defineProps<{
  accounts?: Array<any>;
  title?: string;
}>();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const fields = ref([
  {
    key: 'show',
    label: '',
  },
  {
    key: 'type',
    sortable: true,
    label: t('account/workflow/action'),
  },
  {
    key: 'task.oldAccountHolder',
    sortable: true,
    label: t('account/workflow/User'),
  },
  {
    key: 'task.company',
    sortable: true,
    label: t('account/workflow/Company'),
  },
  {
    key: 'task.doneMA',
    label: t('account/workflow/MA'),
  },
  {
    key: 'task.doneEO',
    label: t('account/workflow/EO'),
  },
  {
    key: 'task.doneIT',
    label: t('account/workflow/IT'),
  },
  {
    key: 'task.doneVO',
    label: t('account/workflow/VO'),
  },
  {
    key: 'task.doneOV',
    label: t('account/workflow/OV'),
  },
  {
    key: 'task.donePA',
    label: t('account/workflow/PA'),
  },
  {
    key: 'lastModified',
    label: t('account/workflow/lastmodified'),
    sortable: true,
  },
]);
</script>
