{% set content %}
    <div>
        <form id="selectNumber row">
            <div class="col-xs-2">
                <div class="input-group">
                    <span class="input-group-addon">{{ '_nc'|trans({}, module) }}</span>
                    <input type="number" ng-model="data.nextNumber" class="form-control">
                </div>
            </div>
            <div class="col-xs-2">
                <button type="button" class="btn btn-primary" ng-click="create()">
                    {{ "Create information"|trans({}, 'information') }}
                </button>
            </div>
        </form>
    </div>
{% endset %}

{% include 'Frame/TwigTemplates/portlet.html.twig' with {
    'id': false,
    'class': 'warning mb20',
    'heading': true,
    'iconClass': false,
    'topic': "Please choose number"|trans({}, 'information'),
    'content': content,
    'footer': false,
    'iconRefresh': false,
    'iconToggle': false,
    'iconClose': false,
    'plain': false,
    'movable': false,
    'closedByDefault': false,
    'ngAttributes': {
        'container': {
            0: 'ng-hide="loading"',
            1: 'ng-if="!data.number && data.nextNumber"'
        }
    }
} only %}
