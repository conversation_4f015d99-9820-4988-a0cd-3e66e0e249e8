<template>
  <div v-cloak v-if="isLoaded">
    <Card card-class="header-bg-danger header-color-white mt20" :canToggle="false">
      <template #title>{{ t('frame/news/draft') }}</template>
      <template #body>
        <div class="custom-control custom-checkbox mb5">
          <input
            id="checkbox_draft"
            v-model="internalDraft"
            class="custom-control-input"
            type="checkbox"
            :value="true"
            @change="setDraftValue(internalDraft)" />
          <label class="custom-control-label" for="checkbox_draft" v-html="t('frame/news/draft_description')"></label>
        </div>
      </template>
    </Card>

    <Card card-class="header-bg-abus-blue-light header-color-white mt20" :canToggle="true" :isClosed="true">
      <template v-slot:title>{{ $t('frame/news/predefined_text_header') }}</template>
      <template v-slot:body>
        <div class="row">
          <div class="col-md-3">
            <b-form-group :label="$t('frame/news/predefined_text_usecase')" label-for="use_predefined_text">
              <b-form-select
                id="use_predefined_text"
                v-model="predefinedText"
                :options="[
                  { value: 0, text: '-' },
                  { value: 1, text: $t('frame/news/predefined_text_maintenance_option') },
                ]"
                @change="usePredefinedText()" />
            </b-form-group>
          </div>
          <div v-show="predefinedText === 1" class="col-md-3">
            <b-form-group :label="$t('frame/news/predefined_text_date')" label-for="predefined_text_date">
              <b-form-datepicker
                id="predefined_text_date"
                v-model="predefinedTextDate"
                class="mb-2"
                :value-as-date="true"></b-form-datepicker>
            </b-form-group>
          </div>
          <div v-show="predefinedText === 1" class="col-md-3">
            <div class="row">
              <div class="col-md-6">
                <b-form-group :label="$t('frame/news/predefined_text_time_from')" label-for="predefined_text_time_from">
                  <b-form-timepicker
                    id="predefined_text_time_from"
                    v-model="predefinedTextTimeFrom"
                    class="mb-2"
                    :placeholder="$t('frame/news/predefined_text_time_from')"
                    :minutes-step="15"></b-form-timepicker>
                </b-form-group>
              </div>
              <div class="col-md-6">
                <b-form-group
                  :label="$t('frame/news/predefined_text_time_until')"
                  label-for="predefined_text_time_until">
                  <b-form-timepicker
                    id="predefined_text_time_until"
                    v-model="predefinedTextTimeUntil"
                    class="mb-2"
                    :placeholder="$t('frame/news/predefined_text_time_until')"
                    :minutes-step="15"></b-form-timepicker>
                </b-form-group>
              </div>
            </div>
          </div>
          <div class="col-md-3 align-self-end">
            <b-button class="mb-3" variant="primary" @click="setDateAndTime()">
              {{ $t('frame/news/predefined_text_setDateAndTime') }}
            </b-button>
          </div>
        </div>
      </template>
    </Card>

    <Card card-class="header-bg-abus-blue-dark header-color-white mt20" :canToggle="true">
      <template #title>
        <img class="flag" :src="flagDe" />
        {{ t('apis/german') }}
      </template>
      <template #body>
        <NewsCreateBlock id="createBlockGerman" lang="de_DE" />
      </template>
    </Card>

    <Card
      card-class="header-bg-abus-blue-dark header-color-white mt20"
      :canToggle="true"
      :isClosed="!hasContent('en_GB')">
      <template #title>
        <img class="flag" :src="flagGb" />
        {{ t('apis/english') }}
      </template>
      <template #body>
        <NewsCreateBlock id="createBlockEnglish" lang="en_GB" />
      </template>
    </Card>

    <Card
      card-class="header-bg-abus-blue-dark header-color-white mt20"
      :canToggle="true"
      :isClosed="!hasContent('fr_FR')">
      <template #title>
        <img class="flag" :src="flagFr" />
        {{ t('apis/french') }}
      </template>
      <template #body>
        <NewsCreateBlock id="createBlockFrench" lang="fr_FR" />
      </template>
    </Card>

    <Card
      card-class="header-bg-abus-blue-dark header-color-white mt20"
      :canToggle="true"
      :isClosed="!hasContent('es_ES')">
      <template #title>
        <img class="flag" :src="flagEs" />
        {{ t('apis/spanish') }}
      </template>
      <template #body>
        <NewsCreateBlock id="createBlockSpanish" lang="es_ES" />
      </template>
    </Card>

    <Card
      card-class="header-bg-primary header-color-white mt20"
      :canToggle="true"
      :isClosed="selectedModules.length === 0">
      <template v-slot:title>{{ $t('frame/news/modules') }}</template>
      <template v-slot:body>
        <div class="row">
          <div v-for="(module, key) in getModules" :key="key" class="col-md-3" style="margin-bottom: 20px">
            <div class="custom-control custom-checkbox mb5">
              <input
                :id="'checkbox_receivers_' + key"
                v-model="selectedModules[module.uniqueIdentifier]"
                class="custom-control-input"
                type="checkbox"
                :value="true"
                @change="
                  setSelectedModule({
                    uniqueIdentifier: module.uniqueIdentifier,
                    name: module.name,
                    parentUniqueIdentifier: '',
                    parentName: '',
                  })
                " />
              <label
                class="custom-control-label bold"
                :for="'checkbox_receivers_' + key"
                v-html="$t(module.name)"></label>
            </div>

            <div v-for="(subModule, subKey) in module.subModules" style="margin-left: 30px">
              <div class="custom-control custom-checkbox mb5">
                <input
                  :id="'checkbox_receivers_' + key + '_' + subKey"
                  v-model="selectedModules[subModule.uniqueIdentifier]"
                  class="custom-control-input"
                  type="checkbox"
                  :value="true"
                  @change="
                    setSelectedModule({
                      uniqueIdentifier: subModule.uniqueIdentifier,
                      name: subModule.name,
                      parentUniqueIdentifier: subModule.parentUniqueIdentifier,
                      parentName: subModule.parentName,
                    })
                  " />
                <label
                  class="custom-control-label"
                  :for="'checkbox_receivers_' + key + '_' + subKey"
                  v-html="$t(subModule.name)"></label>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Card>

    <Card
      card-class="header-bg-primary header-color-white mt20"
      :canToggle="true"
      :isClosed="selectedGroups.length === 0">
      <template v-slot:title>{{ $t('frame/news/groups') }}</template>
      <template v-slot:body>
        <div class="row">
          <div class="col-md-6">
            <div
              v-for="(value, group, index) in getGroups"
              v-show="index < halfGroupsCount"
              class="custom-control custom-checkbox mb5">
              <input
                :id="'checkbox_groups_' + index"
                v-model="selectedGroups[group]"
                class="custom-control-input"
                type="checkbox"
                :value="value"
                @change="setSelectedGroup({ name: group, value: selectedGroups[group] })" />
              <label class="custom-control-label" :for="'checkbox_groups_' + index" v-html="group"></label>
            </div>
          </div>

          <div class="col-md-6">
            <div
              v-for="(value, group, index) in getGroups"
              v-show="index >= halfGroupsCount"
              class="custom-control custom-checkbox mb5">
              <input
                :id="'checkbox_groups_' + index"
                v-model="selectedGroups[group]"
                class="custom-control-input"
                type="checkbox"
                :value="value"
                @change="setSelectedGroup({ name: group, value: selectedGroups[group] })" />
              <label class="custom-control-label" :for="'checkbox_groups_' + index" v-html="group"></label>
            </div>
          </div>
        </div>
      </template>
    </Card>

    <Card card-class="header-bg-primary header-color-white mt20">
      <template v-slot:title>{{ $t('apis/admin/publishing') }}</template>
      <template v-slot:body>
        <div class="row">
          <div class="col-md-4">
            <div class="input-group" style="color: #000000">
              <div class="input-group-prepend">
                <div class="input-group-text">
                  <font-awesome-icon fixed-width icon="calendar"></font-awesome-icon>
                </div>
              </div>
              <!-- TODO: Replace date-picker component -->
              <!--              <date-picker
                v-model="publishDate"
                :config="config"
                :wrap="true"
                :placeholder="$t('frame/news/publishDate')"></date-picker>-->
            </div>
          </div>
          <div class="col-md-4">
            <b-form-checkbox id="sendInformationMail" v-model="sendInformationMail" name="sendInformationMail">
              {{ $t('frame/news/sendInformationMail') }}
            </b-form-checkbox>
          </div>
        </div>
      </template>
    </Card>

    <div class="row store mb-3">
      <div class="col-md-3">
        <button class="btn btn-danger" @click="remove">
          <font-awesome-icon class="mr5" :icon="['fas', 'trash-alt']"></font-awesome-icon>
          {{ $t('apis/admin/delete') }}
        </button>
      </div>
      <div class="col-md-3"></div>
      <div class="col-md-3">
        <button class="btn btn-warning" @click="save">
          <font-awesome-icon class="mr5" :icon="['far', 'hdd']"></font-awesome-icon>
          {{ $t('apis/admin/store') }}
        </button>
      </div>
      <div class="col-md-3">
        <button class="btn btn-success" @click="close">
          <font-awesome-icon class="mr5" icon="sign-out"></font-awesome-icon>
          {{ $t('apis/admin/storeAndClose') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed, getCurrentInstance } from 'vue';
import dayjs from 'dayjs';
import Card from '@assets/protected/vue/component/helper/Card.vue';
import NewsCreateBlock from '@assets/protected/vue/component/news/NewsCreateBlock.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { useNewsStore } from '@assets/protected/vue/stores/useNewsStore';

import flagDe from '@flags/png100px/de.png';
import flagGb from '@flags/png100px/gb.png';
import flagFr from '@flags/png100px/fr.png';
import flagEs from '@flags/png100px/es.png';

const props = defineProps<{
  edit?: number;
}>();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const langMap = {
  de: 'de_DE',
  en: 'en_GB',
  fr: 'fr_FR',
  es: 'es_ES',
};

const newsStore = useNewsStore();

const isLoaded = computed(() => newsStore.getIsLoaded);
const draft = computed(() => newsStore.getDraft);
const getModules = computed(() => newsStore.getModules);
const getSelectedModules = computed(() => newsStore.getSelectedModules);
const getGroups = computed(() => newsStore.getGroups);
const getSelectedGroups = computed(() => newsStore.getSelectedGroups);
const getContent = computed(() => newsStore.getContent);
const hasContent = (lang: string) => newsStore.hasContent(lang);
const getPublishdate = computed(() => newsStore.getPublishdate);
const getSendInformationMail = computed(() => newsStore.getSendInformationMail);
const halfGroupsCount = computed(() => newsStore.getHalfGroupsCount);

const internalDraft = ref(true);
const selectedModules = ref<any>({});
const selectedGroups = ref<any>({});

const predefinedText = ref(0);
const predefinedTextDate = ref(new Date());
const predefinedTextTimeFrom = ref('07:00');
const predefinedTextTimeUntil = ref('15:30');

const publishDate = ref('');
const sendInformationMail = ref(false);

onMounted(() => {
  newsStore.load({ edit: props.edit });
  internalDraft.value = draft.value;
});

watch(draft, (val) => (internalDraft.value = val));
watch(getSelectedModules, (val) => (selectedModules.value = val));
watch(getSelectedGroups, (val) => (selectedGroups.value = val));

watch(publishDate, (val, oldVal) => {
  if (isLoaded.value && val !== oldVal && val !== null) {
    newsStore.setPublishDate(dayjs(val).format('YYYY-MM-DD'));
  }
});

watch(getPublishdate, (val) => {
  publishDate.value = val ? dayjs.unix(val).format('YYYY-MM-DD') : '';
});

watch(sendInformationMail, (val, oldVal) => {
  if (isLoaded.value && val !== oldVal && val !== null) {
    newsStore.setSendInformationMail(val);
  }
});

watch(getSendInformationMail, (val) => {
  sendInformationMail.value = val ?? false;
});

function usePredefinedText() {
  if (predefinedText.value === 1) {
    for (const lang of ['de', 'en', 'fr', 'es']) {
      newsStore.setTitleAndContent({
        content: t(`frame/news/predefined_text_maintenance`, lang),
        title: t(`frame/news/predefined_text_maintenance_header`, lang),
        lang: langMap[lang],
      });
    }
  } else {
    for (const lang of ['de_DE', 'en_GB', 'fr_FR', 'es_ES']) {
      newsStore.setTitleAndContent({
        content: '',
        title: '',
        lang,
      });
    }
  }
}

function setDateAndTime() {
  const regexDate = /\w+, \d{1,2}\.\d{1,2}\.\d{4}/gm;
  const regexTime = /\d{1,2}:\d{2}/gm;
  const date = dayjs(predefinedTextDate.value);

  for (const lang of ['de_DE', 'en_GB', 'fr_FR', 'es_ES']) {
    const text = (getContent.value[lang]?.content || '').replace(regexDate, date.format('dddd, DD.MM.YYYY'));
    let matchCount = 0;
    const updated = text.replace(regexTime, () => {
      return matchCount++ === 0 ? predefinedTextTimeFrom.value : predefinedTextTimeUntil.value;
    });
    newsStore.setTitleAndContent({
      content: updated,
      title: getContent.value[lang]?.title || '',
      lang,
    });
  }
}

function setDraftValue(val: boolean) {
  newsStore.setDraft(val);
}
function setSelectedModule(mod: any) {
  newsStore.setSelectedModules(mod);
}
function setSelectedGroup(grp: any) {
  newsStore.setSelectedGroups(grp);
}
function save() {
  newsStore.save();
}
function remove() {
  newsStore.remove();
}
function close() {
  newsStore.close();
}
</script>

<style>
.flag {
  width: 25px;
  height: 15px;
  margin-right: 10px;
}
</style>

<style scoped lang="scss">
.bold {
  font-weight: bold;
}
</style>
