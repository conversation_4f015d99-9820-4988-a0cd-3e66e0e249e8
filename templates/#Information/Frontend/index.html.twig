{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/information/information.js') }}"></script>

    {#<script type="text/javascript">#}
        {#$(document).ready(function() {#}

            {#// Date Range für die Suche initialisieren und mit den richtigen Spracheinstellungen versehen#}
            {#$('.input-daterange input').each(function() {#}
                {#$(this).datepicker({#}
                    {#{% if getLangCode() == 'ger' %}language: "de",{% endif %}#}
                    {#{% if getLangCode() == 'fre' %}language: "fr",{% endif %}#}
                    {#{% if getLangCode() == 'esl' %}language: "es",{% endif %}#}
                    {#todayHighlight: true,#}
                    {#autoclose: true#}
                {#});#}
            {#});#}
        {#});#}
    {#</script>#}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/information/information.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block ng_init_body %}id="information" ng-controller="InformationController" ng-init="section='{{ module }}'; getSearchExtended();"{% endblock %}

{% block administration %}
    {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' %}
        {% block administration %}
            {{ parent() }}

            <div class="sidebar-panel">
                <h5 class="sidebar-panel-title">
                    {{ ["apis/", module, "/headline_short"]|join|trans({}, 'across') }}
                </h5>
            </div>

            <div class="content" >
                <a href="{{ path('abus_information_administration', {'section': module, 'domain': getDomain(), 'subdomain': getSubdomain('portal') }) }}" class="btn btn-primary btn-block mr5">{{ "Administration"|trans({}, 'information') }}</a>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block headline %}{{ "_headline"|trans({}, module) }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block search %}{% include "Information/Parts/search.html.twig" %}{% endblock %}

{% block content %}

    <div id="salesInformation" ng-if="extendedSearchResults" ng-cloak>
        <div class="container-fluid salesInformations" infinite-scroll="loadMore()" infinite-scroll-disabled='busy' infinite-scroll-distance="1">

            <div class="well well-sm" ng-if="init == false && foundRows" ng-bind-html="foundRows + ' {{ "search results found."|trans({}, "information") }}'"></div>

            <div ng-repeat="result in results track by $index">
                {% include 'Information/Parts/row_information.html.twig' %}
            </div>
        </div>
    </div>

    <div id="loading" ng-show="extendedSearchLoading"></div>
    <div id="noSearchResultsFound" ng-show="foundRows == 0 && !extendedSearchLoading" ng-cloak class="alert alert-danger">
        {{ "No search results found."|trans({}, 'information') }}
    </div>

{% endblock %}

{% block custom_javascripts %}{% endblock %}
