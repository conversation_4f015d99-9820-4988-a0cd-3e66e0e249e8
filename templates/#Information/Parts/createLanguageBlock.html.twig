{% if language == 'german' %}

    {% set lang = 'ger' %}
    {% set flag = 'de' %}

{% elseif language == 'english' %}

    {% set lang = 'eng' %}
    {% set flag = 'gb' %}

{% elseif language == 'french' %}

    {% set lang = 'fre' %}
    {% set flag = 'fr' %}

{% elseif language == 'spanish' %}

    {% set lang = 'esl' %}
    {% set flag = 'es' %}

{% endif %}

<div class="languageBlock">

    <div class="row">
        <div class="panel panel-primary toggleCustom">
            <div class="panel-heading mark" ng-click="toggleMaxMin($event, 'langBlock{{ lang|capitalize }}')">
                <h4 class="panel-title"><img src="{{ asset('dist/images/flags/') }}{{ flag }}.png" />&nbsp;&nbsp;&nbsp;{{ language|capitalize|trans({}, 'information') }}
                    <div>
                        <span class="glyphicon" ng-class="{'glyphicon-chevron-up': toggleBlock['langBlock{{ lang|capitalize }}'], 'glyphicon-chevron-down': !toggleBlock['langBlock{{ lang|capitalize }}']}" aria-hidden="true"></span>
                    </div>
                </h4>
            </div>
            <div class="panel-body" ng-show="toggleBlock['langBlock{{ lang|capitalize }}']">


                <div class="inner">
                    <div class="row mb0">

                        <div class="col-md-8">

                            <div class="container-fluid noPadding noMargin">
                                <div class="row">

                                    <div class="col-md-2">
                                        <div class="input-group">
                                            <span class="input-group-addon">{{ '_nc'|trans({}, module) }}</span>
                                            <input type="text" class="form-control" name="readonly" readonly="" ng-model="data.nextNumber" >
                                        </div>
                                    </div>

                                    <div class="col-md-10">
                                        <input type="text" id="contentTitle{{ lang|capitalize }}" class="form-control" ng-model="content.title.{{ lang|lower }}" ng-blur="save('contentTitle{{ lang|capitalize }}')" ng-class="{'stored': visualizeStore['contentTitle{{ lang|capitalize }}']}" placeholder="{{ 'Title'|trans({}, 'information') }}">
                                    </div>

                                </div>

                                <div id="editor{{ lang|capitalize }}" class="row mb0">
                                    <div class="col-md-12">
                                        <div ng-class="{'stored': visualizeStore['ckeditor{{ lang|capitalize }}']}">
                                            <div ckeditor="options{{ lang|capitalize }}" id="ckeditor{{ lang|capitalize }}" ng-model="content.content.{{ lang|lower }}" ready="onReady('{{ lang|lower }}')"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div id="equal{{ lang|capitalize }}" class="col-md-4 dropzoneContainer">
                            {% set content %}
                                 <form action="/"
                                        id="dropzone{{ lang|capitalize }}"
                                        class="dropzone"
                                        method="post"
                                        enctype="multipart/form-data"
                                        ng-dropzone
                                        dropzone="ctrl.dropzone{{ lang|capitalize }}"
                                        dropzone-config="ctrl.dropzoneConfig"
                                        event-handlers="{ 'error': ctrl.dzError, 'successmultiple': ctrl.dzSuccess }">
                                        <input type="hidden" name="sender" value="dropzone">
                                        <input type="hidden" name="language" value="{{ lang|lower }}">
                                        <div class="dz-message" data-dz-message>
                                            <span>
                                                <i class="fa fa-dropbox fa-3x" aria-hidden="true"></i>
                                                &nbsp;&nbsp;
                                                {{ "Drop attachments here to upload"|trans({}, 'information') }}
                                            </span>
                                        </div>
                                </form>

                                <table class="attachment">
                                    <tr ng-repeat="attachment in data.attachments track by $index" ng-if="attachment.language.indexOf('{{ lang|lower }}') != -1 && attachment.type == 'attachment'">
                                        <td class="name" ng-bind-html="attachment.originalFilename"></td>
                                        <td class="preview">
                                            <a ng-if="attachment.mimeType.indexOf('image') != -1" ng-href="/information/view/image/{% verbatim %}{{ data.section }}{% endverbatim %}/{% verbatim %}{{ data.number }}{% endverbatim %}/{% verbatim %}{{ attachment.filename }}{% endverbatim %}" data-lightbox="lightboxDropzone{{ lang|capitalize }}" data-title="{% verbatim %}{{ attachment.originalFilename }}{% endverbatim %}">
                                                <img ng-src="/information/view/image/{% verbatim %}{{ data.section }}{% endverbatim %}/{% verbatim %}{{ data.number }}{% endverbatim %}/{% verbatim %}{{ attachment.filename }}{% endverbatim %}">
                                            </a>
                                            <a ng-if="attachment.mimeType.indexOf('image') == -1" ng-href="/information/view/attachment/{% verbatim %}{{ data.section }}{% endverbatim %}/{% verbatim %}{{ data.number }}{% endverbatim %}/{% verbatim %}{{ attachment.filename }}{% endverbatim %}" target="_blank">
                                                <i class="fa fa-file-text-o" aria-hidden="true"></i>
                                            </a>

                                        </td>
                                        <td class="size">{% verbatim %}{{ attachment.sizeAsMB }}{% endverbatim %} MB</td>
                                        <td class="icon"><i class="fa fa-share-alt" aria-hidden="true" title="{{ "Share with other languages"|trans({}, 'information') }}" ng-show="attachment.language.length == 1 && attachment.type == 'attachment'" ng-click="moveAttachmentToGlobal($index)"></i></td>
                                        <td class="icon"><i class="fa fa-trash-o" aria-hidden="true" title="{{ "Remove"|trans({}, 'information') }}" ng-click="deleteAttachment($index, '{{ lang|lower }}')"></i></td>
                                    </tr>
                                </table>


                            {% endset %}

                            {% include 'Frame/TwigTemplates/portlet.html.twig' with {
                                'id': false,
                                'class': 'primary',
                                'heading': true,
                                'iconClass': "fa fa-file-text-o",
                                'topic': "Attachments"|trans({}, 'information'),
                                'content': content,
                                'footer': false,
                                'iconRefresh': false,
                                'iconToggle': false,
                                'iconClose': false,
                                'plain': false,
                                'movable': false,
                                'closedByDefault': false
                            } only %}

                            {% set content %}
                                <table class="embeddedImages">
                                    <tr ng-repeat="attachment in data.attachments track by $index" ng-if="(attachment.language.indexOf('{{ lang|lower }}') != -1 || attachment.language.length == 0) && attachment.type == 'image'">
                                        <td class="name" ng-bind-html="attachment.originalFilename" ng-class="{'global': attachment.language == 'global' || attachment.language == ''}"></td>
                                        <td class="preview">
                                            <a ng-href="/information/view/image/{% verbatim %}{{ data.section }}{% endverbatim %}/{% verbatim %}{{ data.number }}{% endverbatim %}/{% verbatim %}{{ attachment.filename }}{% endverbatim %}" data-lightbox="lightbox{{ lang|capitalize }}" data-title="{% verbatim %}{{ attachment.originalFilename }}{% endverbatim %}">
                                                <img ng-src="/information/view/image/{% verbatim %}{{ data.section }}{% endverbatim %}/{% verbatim %}{{ data.number }}{% endverbatim %}/{% verbatim %}{{ attachment.filename }}{% endverbatim %}">
                                            </a>
                                        </td>
                                        <td class="size">{% verbatim %}{{ attachment.sizeAsMB }}{% endverbatim %} MB</td>
                                        <td class="icon"><i class="fa fa-share-alt" aria-hidden="true" title="{{ "Share with other languages"|trans({}, 'information') }}" ng-show="(attachment.language.length == 0 || attachment.language.length == 1) && attachment.type == 'image'" ng-click="moveAttachmentToGlobal($index)"></i></td>
                                        <td class="icon"><i class="fa fa-trash-o" aria-hidden="true" title="{{ "Remove"|trans({}, 'information') }}" ng-click="deleteAttachment($index, '{{ lang|lower }}')"></i></td>
                                    </tr>
                                </table>
                            {% endset %}

                            {% include 'Frame/TwigTemplates/portlet.html.twig' with {
                                'id': false,
                                'class': 'primary mt20',
                                'heading': true,
                                'iconClass': "fa fa-picture-o",
                                'topic': "Embedded images"|trans({}, 'information'),
                                'content': content,
                                'footer': false,
                                'iconRefresh': false,
                                'iconToggle': false,
                                'iconClose': false,
                                'plain': false,
                                'movable': false,
                                'closedByDefault': false
                            } only %}
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
