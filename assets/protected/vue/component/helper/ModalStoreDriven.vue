<template>
  <div id="modal">
    <b-modal
      ref="modal"
      size="lg"
      centered
      no-fade
      no-close-on-backdrop
      hide-header-close
      :title="title"
      :class="[cssClass]">
      <div v-if="vueComponent"><component :is="vueComponent"></component></div>
      <p v-else v-html="message"></p>

      <template v-if="buttons === false" v-slot:modal-footer>
        <div class="w-100 d-flex justify-content-end gap-2">
          <b-btn variant="primary" @click="click({ identifier: 'ok' })" v-html="$t('modal/ok')"></b-btn>
          <b-btn variant="secondary" @click="hide()" v-html="$t('modal/cancel')"></b-btn>
        </div>
      </template>
      <template v-else v-slot:modal-footer>
        <div class="w-100 d-flex justify-content-end gap-2">
          <b-btn
            v-for="button in buttons"
            :key="button.index"
            :variant="button.variant"
            @click="
              click({ identifier: button.clickIdentifier, value: button.returnValue, openNewTab: button.openNewTab })
            "
            v-html="button.title"></b-btn>
          <b-btn variant="secondary" @click="hide()" v-html="$t('modal/cancel')"></b-btn>
        </div>
      </template>
    </b-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import { useModalStore } from '@assets/protected/vue/stores/useModalStore';

interface ModalButton {
  index: number;
  variant: string;
  clickIdentifier: string;
  returnValue?: any;
  openNewTab?: string;
  title: string;
}

interface ClickPayload {
  identifier: string;
  value?: any;
  openNewTab?: string;
}

const modal = ref<any>(null);
const modalStore = useModalStore();

const show = computed(() => modalStore.getShow);
const cssClass = computed(() => modalStore.getCssClass);
const message = computed(() => modalStore.getMessage);
const vueComponent = computed(() => modalStore.getVueComponent);
const title = computed(() => modalStore.getTitle);
const buttons = computed<ModalButton[] | false>(() => modalStore.getButtons);

const hide = () => {
  modalStore.hideModal();
};

const click = (payload: ClickPayload) => {
  modalStore.clickModal(payload);
};

watch(show, (newShow: boolean) => {
  if (modal.value) {
    newShow ? modal.value.show() : modal.value.hide();
  }
});
</script>

<style lang="scss">
@import '@assets/scss/abstract/variables';

div#modal {
  .modal-backdrop {
    z-index: 1000;
    opacity: 0.6;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  header.modal-header {
    min-height: 50px;
    h5 {
      float: left;
    }
  }

  .modal-content {
    background-color: transparent;
  }

  div.modal-body {
    //overflow: scroll;
    overflow: auto;
    width: 100%;
    max-height: 50vh !important;
    background-color: $white;

    div.message {
      font-size: 24px;
    }

    div.ghost {
      img {
        width: 100%;
      }
    }
  }

  footer.modal-footer {
    background-color: $abus-creme;
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;
  }

  div.primary {
    header.modal-header {
      color: $white;
      background-color: $brand-primary;

      button {
        color: $white;
      }
    }
  }

  div.secondary {
    header.modal-header {
      color: $white;
      background-color: $brand-secondary;

      button {
        color: $white;
      }
    }
  }

  div.error,
  div.danger {
    header.modal-header {
      color: $white;
      background-color: $brand-danger;

      button {
        color: $white;
      }
    }
  }

  div.success {
    header.modal-header {
      color: $white;
      background-color: $brand-success;

      button {
        color: $white;
      }
    }
  }

  div.info {
    header.modal-header {
      color: $white;
      background-color: $brand-info;

      button {
        color: $white;
      }
    }
  }

  div.abus-blue-dark {
    header.modal-header {
      color: $white;
      background-color: $abus-blue-dark;

      button {
        color: $white;
      }
    }
  }

  footer {
    button.btn {
      margin-left: 20px;
    }
  }
}
</style>
