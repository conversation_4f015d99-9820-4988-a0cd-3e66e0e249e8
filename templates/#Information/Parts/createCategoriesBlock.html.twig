<div class="row">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h4 class="panel-title">{{ "Categories"|trans({}, 'information') }}</h4>
        </div>
        <div class="panel-body" style="display: block;">

            <div class="row" style="margin-bottom: 0;">

                <div class="col-md-3" ng-repeat="(key, category) in data.available_categories track by $index">
                    <div class="checkbox-custom">
                        <input type="checkbox" value="true" id="category_{% verbatim %}{{ $index }}{% endverbatim %}" ng-model="data.categories[key]" ng-change="save('category_{% verbatim %}{{ $index }}{% endverbatim %}')">
                        <label for="{category_{% verbatim %}{{ $index }}{% endverbatim %}" ng-bind-html="category" ng-class="{'stored': visualizeStore['category_{% verbatim %}{{ $index }}{% endverbatim %}']}"></label>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>
