<template>
  <div id="sideSearch" class="sidebar-panel">
    <h5 class="sidebar-panel-title">
      <i class="fas fa-cloud-download-alt mr5"></i>{{ $t('box/admin/administration') }}
    </h5>

    <div class="content">
      <button
        class="btn btn-sm btn-abus-blue-dark btn-block"
        type="button"
        @click="onCreateIndexTask({ enrichForSearch: false })">
        {{ $t('box/admin/index') }}<br /><span style="font-size: 0.8em; font-weight: bold">{{
          $t('box/admin/notSoOften')
        }}</span>
      </button>
      <button
        class="btn btn-sm btn-danger btn-block"
        type="button"
        @click="onCreateIndexTask({ enrichForSearch: true })">
        {{ $t('box/admin/index_search') }}<br /><span style="font-size: 0.8em; font-weight: bold">{{
          $t('box/admin/takeAWhile')
        }}</span>
      </button>
      <!--<button type="button" class="btn btn-sm btn-abus-yellow-dark btn-block" v-html="$t('box/admin/tags')" @click="onCreateTagTask()"></button>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useBoxStore } from '@assets/protected/vue/stores';

type IndexTaskPayload = {
  enrichForSearch: boolean;
};

const boxStore = useBoxStore();

function onCreateIndexTask(payload: IndexTaskPayload): void {
  boxStore.createIndexTask(payload);
}

function onCreateTagTask(): void {
  boxStore.createTagTask();
}
</script>

<style lang="scss" scoped>
@import '@assets/scss/abstract/variables';
</style>
