<div class="salesInformation">
    <a href="/information/view/{{ module }}/{% verbatim %}{{ result.number }}{% endverbatim %}" class="SalesInformation">
        <div class="row header" style="position: relative;">

            <div class="new" ng-show="result.isNew">
                {% if getLanguage() == 'en_gb' %}
                    <img src="/dist/images/information/new.png">
                {% elseif getLanguage() == 'de_de' %}
                    <img src="/dist/images/information/neu.png">
                {% elseif getLanguage() == 'fr_fr' %}
                    <img src="/dist/images/information/nouveau.png">
                {% elseif getLanguage() == 'es_es' %}
                    <img src="/dist/images/information/nuevo.png">
                {% endif %}
            </div>

            <div class="col-md-1" ng-class="{newPadding: result.isNew}" ng-bind-html="result.number_prefix + ' - ' + result.number"></div>

            <div class="col-md-7">
                <div ng-if="result.readPercentage !== false && result.readPercentage <= 33" title="{% verbatim %}{{ result.readPercentage }}{% endverbatim %} %" style="background-color: #ff4f00; width: 10px; height: 10px; float: left; margin: 5px 0 0 -20px; border-radius: 10px;"></div>
                <div ng-if="result.readPercentage !== false && result.readPercentage > 33 && result.readPercentage <= 66" title="{% verbatim %}{{ result.readPercentage }}{% endverbatim %} %" style="background-color: #ffaa00; width: 10px; height: 10px; float: left; margin: 5px 0 0 -20px; border-radius: 10px;"></div>
                <div ng-if="result.readPercentage !== false && result.readPercentage > 66" title="{% verbatim %}{{ result.readPercentage }}{% endverbatim %} %" style="background-color: #53c200; width: 10px; height: 10px; float: left; margin: 5px 0 0 -20px; border-radius: 10px;"></div>

                <div ng-show="result.title" ng-bind-html="result.title"></div>
                <div ng-hide="result.title" style="font-style: italic; font-weight: normal;">{{ "Title not set"|trans({}, "information") }}</div>
            </div>

            <div class="col-md-1">
                <img src="{{ asset('dist/images/flags/de.png') }}" ng-if="result.available_languages.indexOf('de_DE') > -1" />
                <img src="{{ asset('dist/images/flags/gb.png') }}" ng-if="result.available_languages.indexOf('en_GB') > -1" />
                <img src="{{ asset('dist/images/flags/fr.png') }}" ng-if="result.available_languages.indexOf('fr_FR') > -1" />
                <img src="{{ asset('dist/images/flags/es.png') }}" ng-if="result.available_languages.indexOf('es_ES') > -1" />
            </div>

            <div class="col-md-1">
                <img title="{% verbatim %}{{ result.author }}{% endverbatim %}" height="23" ng-src="{% verbatim %}{{ result.author_image }}{% endverbatim %}" ng-if="result.author_image" />
                <i class="fa fa-user fa-lg" aria-hidden="true" title="{% verbatim %}{{ result.author }}{% endverbatim %}" ng-if="!result.author_image"></i>
                <i class="fa fa-paperclip fa-lg" aria-hidden="true" title="{{ "Attachments"|trans({}, "information") }}" style="margin-left: 20px;" ng-if="result.hasAttachments"></i>
            </div>

            <div class="col-md-1" ng-init="setFavorite(result.number, result.isFavorite)" ng-cloak>
                <img class="removeFavorite" src="{{ asset('/dist/images/information/favorite.png') }}" title="{{ "Remove from favorite list"|trans({}, 'information') }}" ng-click="removeFavorite(result.section, result.number); $event.preventDefault();" ng-show="favorites[result.number]" />
                <img class="addFavorite" src="{{ asset('/dist/images/information/nofavorite.png') }}" title="{{ "Add to favorite list"|trans({}, 'information') }}" ng-click="addFavorite(result.section, result.number); $event.preventDefault();" ng-hide="favorites[result.number]"/>
            </div>

            <div class="col-md-1" ng-bind-html="result.release_date"></div>

        </div>

        <div class="row content">
            <div class="col-md-12" ng-show="result.teaser != ''" ng-bind-html="result.teaser + '...'"></div>
        </div>

        <div class="row footer" ng-if="result.categories.length > 0">
            <div class="col-md-1">
                {{ "Categories"|trans({}, 'sales_common') }}:
            </div>
            <div class="col-md-10">
                <span class="label label-default mr10 mb10" ng-repeat="category in result.categories" ng-bind-html="category"></span>
            </div>
        </div>
    </a>

    <div class="row reference" ng-repeat="reference in result.references" ng-class="{draft: reference.draft, notPublished: !reference.published}">
       {% include 'Information/Parts/reference.html.twig' %}
    </div>
</div>
