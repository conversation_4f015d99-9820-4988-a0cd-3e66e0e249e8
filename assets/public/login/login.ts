import '@assets/public/login/login.scss';
import Vue from 'vue';
import Axios from 'axios';

new Vue({
  delimiters: ['{*', '*}'],
  el: '#login',
  data: function () {
    return {
      showResetPasswordDialog: <boolean>false,
      sendPasswordRecoveryToEmail: <string>'',
      emailSent: <boolean>false,
      emailSentSuccessfully: <boolean>false,
      backendMessage: <string>'',
      recoveryRoute: <string>'',
    };
  },
  mounted: function (): void {
    const usernameInput = <HTMLInputElement>document.getElementById('username');
    usernameInput!.focus();
    usernameInput!.select();
  },
  methods: {
    requestNewPassword($event: Event): void {
      $event.preventDefault();

      const body = new URLSearchParams({
        email: this.sendPasswordRecoveryToEmail,
      });

      Axios.post(this.recoveryRoute, body, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      })
        .then((response: any) => {
          this.emailSent = true;
          this.emailSentSuccessfully = response.data.successful;
          this.backendMessage = response.data.message;
        })
        .catch((error: any) => {
          console.log(error);
          this.emailSent = false;
          this.emailSentSuccessfully = false;
        });
    },
  },
});
