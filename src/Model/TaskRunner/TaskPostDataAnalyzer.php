<?php

/**
 * Ersteller: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 20.06.17 11:02.
 */

namespace App\Model\TaskRunner;

use App\Exception\InvalidTaskPostDataException;

class TaskPostDataAnalyzer
{
    /**
     * Sanitizing POST Data.
     *
     * @throws InvalidTaskPostDataException
     */
    public function analyzePostData(array $postData): TaskPostData
    {
        $taskPostData = new TaskPostData();

        $bundleName = $postData['bundleName'] ?? null;
        $task = $postData['task'] ?? null;
        $parameters = $postData['parameters'] ?? [];

        $taskPostData->bundleName = filter_var($bundleName, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);
        $taskPostData->task = filter_var($task, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);

        foreach ($parameters as $key => $value) {
            $key = filter_var($key, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);
            $value = filter_var($value, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);

            // True bzw. False als Value erkennen
            if ('true' === $value) {
                $value = true;
            } elseif ('false' === $value) {
                $value = false;
            }

            $taskPostData->parameters[$key] = $value;
        }

        if (empty($taskPostData->bundleName) || empty($taskPostData->task)) {
            throw new InvalidTaskPostDataException();
        }

        return $taskPostData;
    }
}
