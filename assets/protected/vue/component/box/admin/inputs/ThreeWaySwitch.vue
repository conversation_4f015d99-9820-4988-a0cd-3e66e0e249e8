<template>
  <div class="row">
    <div class="label col-sm-3" v-html="t(label)"></div>

    <div class="switch btn-group col-sm-4">
      <label :class="{ selected: value === null }">
        Nicht gesetzt
        <input :id="inode + 'not_set'" v-model="value" type="radio" :value="null" />
      </label>

      <label class="bg-success text-white" :class="{ selected: value === 'true' }">
        An
        <input v-model="value" type="radio" value="true" />
      </label>

      <label class="bg-danger text-white" :class="{ selected: value === 'false' }">
        Aus
        <input v-model="value" type="radio" value="false" />
      </label>
    </div>

    <div class="description col-sm-5" v-html="t(description)"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, onMounted, watch } from 'vue';
import { useBoxStore } from '@assets/protected/vue/stores';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const boxStore = useBoxStore();

interface Props {
  label: string;
  description: string;
  inode: number;
  category: string;
  setting: string;
}

const props = defineProps<Props>();

const getValue = computed(() => boxStore.getValue);
const setValue = (payload: any) => boxStore.setValue(payload);

const value = ref<any>(null);

// Watch for value changes
watch(value, (newValue: any) => {
  setValue({ inode: props.inode, category: props.category, setting: props.setting, value: newValue });
});

onMounted(() => {
  value.value = getValue.value(props.inode, props.category, props.setting);
});
</script>

<style lang="scss" scoped>
.row {
  .label {
    font-weight: bold;
    line-height: 30px;
  }

  .switch {
    label {
      cursor: pointer;
      display: block;
      float: left;
      line-height: 30px;
      height: 30px;
      min-width: 70px;
      padding: 0 10px;
      background-color: #ccc;
      text-align: center;
      opacity: 0.3;

      &:first-of-type {
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
      }

      &:last-child {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
      }

      &.selected {
        opacity: 1;
      }
    }

    input[type='radio'] {
      left: -999em;
      position: absolute;
    }
  }

  .description {
    font-size: 0.8rem;
  }
}
</style>
