{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/information/information.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/information/information.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block ng_init_body %}id="information" ng-controller="SingleInformationController" ng-init="section = '{{ section }}'; loading = true; load('{{ section }}', {{ number }});"{% endblock %}

{% block administration %}
    {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' %}
        {% block administration %}
            {{ parent() }}

            <div class="sidebar-panel">
                <h5 class="sidebar-panel-title">
                    {{ ["apis/", module, "/headline_short"]|join|trans({}, 'across') }}
                </h5>
            </div>

            <div class="content" >
                <a href="{{ path('abus_information_administration', {'section': module, 'domain': getDomain(), 'subdomain': getSubdomain('portal') }) }}" class="btn btn-primary btn-block mr5">{{ "Administration"|trans({}, 'information') }}</a>
                <a href="{{ path('abus_information_administration_edit', {'number': number, 'section': module, 'domain': getDomain(), 'subdomain': getSubdomain('portal') }) }}" class="btn btn-success btn-block mr5">{{ "Edit this information"|trans({}, 'information') }}</a>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% set showPageHeader = false %}

{% block content %}

    {% include 'Information/Parts/information.html.twig' %}

    <div id="loading" ng-show="loading"></div>

{% endblock %}

{% block custom_javascripts %}{% endblock %}
