{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "User management"|trans({}, 'usermanagement') }} | ABUS Kransysteme GmbH{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/userManagement/userManagement.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/userManagement/userManagement.css') }}" rel="stylesheet" type="text/css">
{% endblock %}

{% block administration %}
    {{ parent() }}

    {#<div class="sidebar-panel">#}
        {#<h5 class="sidebar-panel-title">{{ "Administration"|trans({}, 'frame') }}</h5>#}
    {#</div>#}

    {#<div class="content">#}
        {#<a href="" class="btn btn-success btn-block mr5" data-toggle="modal" data-target="#indexModal" ng-click="modalTitleEdit = false; modalTitleNew = true;">{{ "New permission package"|trans({}, 'usermanagement') }}</a>#}
    {#</div>#}
{% endblock %}

{% block headline %}{{ 'Permission Packages Administration' }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}


{% block content %}

    <div ng-controller="usermanagementPermissionPackagesController" class="usermanagement">

        <form ng-show="result" class="filter" ng-cloak>
            <div class="form-group">
                <input type="text" id="filterTable" class="form-control" placeholder="{{ 'Filter permission packages'|trans({}, 'usermanagement') }}" ng-model="search" autocomplete="off" ng-change="companyView = [];">
                <button class="btn btn-sm btn-danger" ng-show="search != ''" ng-click="search = ''"><i class="fa fa-times" aria-hidden="true"></i></button>
            </div>
        </form>

        <table id="table-ajax-defer" class="table table-bordered resultTable" cellspacing="0" width="100%" ng-init="loadPermissionPackages()" ng-show="result" ng-cloak>
            <thead>
            <tr>
                <th>{{ 'Module'|trans({}, 'usermanagement') }}</th>
                <th>{{ 'Displayname'|trans({}, 'usermanagement') }}</th>
                <th>{{ 'AD Description'|trans({}, 'usermanagement') }}</th>
                <th>{{ 'AD Group'|trans({}, 'usermanagement') }}</th>
                <th></th>
            </tr>
            </thead>

            <tbody>
                <tr ng-repeat-start="permissionPackage in permissionPackages | permissionPackageFilter:search track by $index" ng-class="{greyBg: $index%2}" class="click">

                    <td ng-bind-html="permissionPackage.module" ng-click="view($index, permissionPackage.name)"></td>

                    <td ng-click="view($index, permissionPackage.name)" style="font-size: 12px;">
                        <div><img src="/bundles/abusframe/img/flags/gb.png" ng-show="permissionPackage.translation.en_gb"> {% verbatim %}{{ permissionPackage.translation.en_gb }}{% endverbatim %}</div>
                        <div><img src="/bundles/abusframe/img/flags/de.png" ng-show="permissionPackage.translation.de_de"> {% verbatim %}{{ permissionPackage.translation.de_de }}{% endverbatim %}</div>
                        <div><img src="/bundles/abusframe/img/flags/fr.png" ng-show="permissionPackage.translation.fr_fr"> {% verbatim %}{{ permissionPackage.translation.fr_fr }}{% endverbatim %}</div>
                        <div><img src="/bundles/abusframe/img/flags/es.png" ng-show="permissionPackage.translation.es_es"> {% verbatim %}{{ permissionPackage.translation.es_es }}{% endverbatim %}</div>
                    </td>

                    <td ng-bind-html="permissionPackage.description" ng-click="view($index, permissionPackage.name)"></td>
                    <td>
                        <button class="btn btn-xs btn-primary mr5" ng-bind-html="permissionPackage.name"></button>
                    </td>

                    <td>
                        <button class="btn btn-xs btn-warning mr5" data-toggle="modal" data-target="#indexModal" >
                            <i class="fa fa-pencil" aria-hidden="true"></i>
                            {{ 'Edit'|trans({}, 'usermanagement') }}
                        </button>
                    </td>
                </tr>
                <tr ng-repeat-end ng-show="permissionPackageView[$index]">
                    <td colspan="5" style="padding: 0;">
                        <div class="viewPermissionPackage">
                            TEST
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>


        <div class="modal fade" id="indexModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" ng-init="loadADGroups(); loadAllModuleNames('{{ getLanguage() }}');">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">
                            <span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title">{{ "Edit permission package"|trans({}, 'usermanagement') }}</h4>
                    </div>
                    <div class="modal-body">

                        <div class="alert alert-danger" ng-show="error" ng-bind-html="error"></div>

                        <form>

                            <div class="form-group">
                                <label for="adgroup">{{ "AD Group"|trans({}, 'usermanagement') }}</label>
                                <input type="text" class="form-control" id="adgroup" ng-model="permissionPackageADGroup" disabled="disabled">
                            </div>


                            <div class="form-group">
                                <label for="module">{{ "Module"|trans({}, 'usermanagement') }}</label>
                                <select ng-model="permissionPackageModule" class="form-control">
                                    <option value=""></option>
                                    <option ng-repeat="moduleName in allModuleNames" ng-bind-html="moduleName" value="{% verbatim %}{{ moduleName }}{% endverbatim %}"></option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="name">{{ "Displayname"|trans({}, 'usermanagement') }}</label>
                                <div class="input-group">
                                    <div class="input-group-addon"><img src="/bundles/abusframe/img/flags/gb.png"></div>
                                    <input type="text" class="form-control" id="name" ng-model="permissionPackageName">
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-group">
                                    <div class="input-group-addon"><img src="/bundles/abusframe/img/flags/de.png"></div>
                                    <input type="text" class="form-control" id="name" ng-model="permissionPackageName">
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-group">
                                    <div class="input-group-addon"><img src="/bundles/abusframe/img/flags/fr.png"></div>
                                    <input type="text" class="form-control" id="name" ng-model="permissionPackageName">
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-group">
                                    <div class="input-group-addon"><img src="/bundles/abusframe/img/flags/es.png"></div>
                                    <input type="text" class="form-control" id="name" ng-model="permissionPackageName">
                                </div>
                            </div>

                        </form>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" ng-click="">{{ "Cancel"|trans({}, 'usermanagement') }}</button>
                        <button type="button" class="btn btn-success" ng-click="storePermissionPackage()" ng-show="modalTitleNew">{{ "Store"|trans({}, 'usermanagement') }}</button>
                        <button type="button" class="btn btn-success" ng-click="updatePermissionPackage($index)" ng-show="modalTitleEdit">{{ "Update"|trans({}, 'usermanagement') }}</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

{% endblock %}



{% block custom_javascripts %}{% endblock %}

{% block footer %}{% endblock %}
