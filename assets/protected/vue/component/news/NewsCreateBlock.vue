<template>
  <div>
    <div class="row">
      <div class="col-md-8">
        <div class="row">
          <div class="col-md-12">
            <input
              v-model="internalTitle"
              class="form-control"
              type="text"
              :placeholder="$t('apis/admin/title')"
              @blur="update" />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mt10">
            <Editor :id="props.id" v-model="internalContent" preset="full" @blur="update" />
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="row">
          <div class="col-md-12 attachmentContainer">
            <Card card-class="header-bg-abus-blue-light header-color-white body-bg-abus-grey-1 body-color-black">
              <template #title>
                <font-awesome-icon icon="file-alt" style="margin-right: 10px" />
                {{ $t('apis/admin/attachmentsAndImages') }}
              </template>

              <template #body>
                <!-- TODO: Replace with Vueuse dropzone: https://vueuse.org/core/useDropZone/ -->
                <Dropzone :uploader="uploader" :multiple="true">
                  <div class="headline">
                    <font-awesome-icon class="fa-inverse" :icon="['fab', 'dropbox']" />
                    {{ $t('apis/admin/dropzone') }}
                  </div>
                  <div class="extensions">
                    (.pdf, .jpg, .jpeg, .png, .gif, .txt, .doc, .docx, .xsl, .xslx, .ppt, .pptx)
                  </div>
                  <div class="size" v-html="$t('apis/admin/maxSize') + ' 5 MB'"></div>
                </Dropzone>

                <div v-for="attachment in getAttachments(props.lang)" :key="attachment.name" class="rowContainer">
                  <div class="row">
                    <div class="col-md-9" v-html="attachment.filename" />
                    <div class="col-md-3" style="float: right; text-align: right">
                      {{ (attachment.filesize / 1048576).toFixed(2) }} MB
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-5">
                      <a
                        v-if="!isImage(attachment.mimetype)"
                        :href="`/news/imageobjects/${internalId}/${attachment.name}`"
                        target="blank">
                        <img class="icon" :src="`/Assets/public/images/fileIcons/32px/${attachment.extension}.png`" />
                      </a>
                      <a v-else data-lightbox="images" :href="`/news/imageobjects/${internalId}/${attachment.name}`">
                        <img :src="`/news/imageobjects/${internalId}/${attachment.name}`" />
                      </a>
                    </div>

                    <div class="col-md-7 buttons">
                      <button
                        v-if="attachment.type === 'image'"
                        class="btn btn-xs btn-primary"
                        @click="flipImageAttachment(attachment)"
                        :title="$t('apis/admin/makeAttachment')"
                        v-html="$t('apis/admin/image')" />
                      <button
                        v-if="attachment.type === 'attachment' && isImage(attachment.mimetype)"
                        class="btn btn-xs btn-primary"
                        @click="flipImageAttachment(attachment)"
                        :title="$t('apis/admin/makeImage')"
                        v-html="$t('apis/admin/attachment')" />
                      <button
                        v-if="attachment.type === 'attachment' && !isImage(attachment.mimetype)"
                        class="btn btn-xs btn-default notClickable"
                        v-html="$t('apis/admin/attachment')" />
                      <button
                        v-if="attachment.language.length < 4 && attachment.language.includes(props.lang)"
                        class="btn btn-xs btn-success"
                        @click="addGlobalAttachment(attachment)"
                        :title="$t('apis/admin/makeGlobal')"
                        v-html="$t('apis/admin/local')" />
                      <button
                        v-if="attachment.language.length === 4"
                        class="btn btn-xs btn-default notClickable"
                        :title="$t('apis/admin/isGlobal')"
                        v-html="$t('apis/admin/global')" />
                      <button
                        class="btn btn-xs btn-danger"
                        @click="removeAttachment(attachment)"
                        :title="$t('apis/admin/removeAttachment')">
                        <font-awesome-icon icon="trash" />
                        {{ $t('apis/admin/delete') }}
                      </button>
                    </div>
                  </div>
                </div>
              </template>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue';
import Card from '@assets/protected/vue/component/helper/Card.vue';
import Editor from '@assets/protected/vue/component/helper/Editor.vue';
// TODO: Replace with Vueuse dropzone: https://vueuse.org/core/useDropZone/
//import Dropzone from 'vue-fineuploader/dropzone.vue';
//import FineUploaderTraditional from 'fine-uploader-wrappers';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { useNewsStore } from '@assets/protected/vue/stores/useNewsStore';
import { useModalStore } from '@assets/protected/vue/stores/useModalStore';

const props = defineProps<{
  id?: string;
  lang: string;
}>();

const internalId = ref(-1);
const internalTitle = ref('');
const internalContent = ref('');
const uploader = ref<any>(null);

const newsStore = useNewsStore();
const modalStore = useModalStore();

const getId = computed(() => newsStore.getId);
const getContent = computed(() => newsStore.getContent);
const getAttachments = (lang: string) => newsStore.getAttachments(lang);

const setTitleAndContent = (payload: any) => newsStore.setTitleAndContent(payload);
const addAttachment = (payload: any) => newsStore.addAttachment(payload);
const addGlobalAttachment = (payload: any) => newsStore.addGlobalAttachment(payload);
const removeAttachment = (payload: any) => newsStore.removeAttachment(payload);
const flipImageAttachment = (payload: any) => newsStore.flipImageAttachment(payload);

watch(getContent, pullChanges, { deep: true });
watch(getId, pullChanges);

function pullChanges() {
  internalId.value = getId.value;
  const content = getContent.value?.[props.lang] || { title: '', content: '' };
  internalTitle.value = content.title;
  internalContent.value = content.content;

  uploader.value = new FineUploaderTraditional({
    options: {
      request: {
        endpoint: `/news/upload/attachment/${internalId.value}/${props.lang}`,
      },
      callbacks: {
        onComplete: (_id: any, _name: any, response: any) => {
          if (response.success) {
            addAttachment(response);
          } else {
            modalStore.showModal({
              cssClass: 'error',
              message: response.message,
            });
          }
        },
      },
    },
  });
}

function update() {
  setTitleAndContent({
    lang: props.lang,
    title: internalTitle.value,
    content: internalContent.value,
  });
}

function isImage(mimetype: string): boolean {
  return mimetype.startsWith('image/');
}

onMounted(pullChanges);
</script>

<style lang="scss">
// Keep global styles
.attachmentContainer {
  .panel-body {
    background-color: #d2e4e8;
    padding-bottom: 0;
  }
}
</style>

<style lang="scss" scoped>
@import '@assets/scss/abstract/variables';

.fine-uploader-dropzone-container {
  background-color: $abus-yellow-dark;
  width: 100%;
  height: 85px;
  font-size: 18px;
  color: #ffffff;
  text-align: center;
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
  border: 1px solid darken($abus-yellow-dark, 5%);
  margin-bottom: 20px;
  box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.75);

  .headline {
    margin-top: 5px;
    line-height: 30px;
  }

  .extensions,
  .size {
    font-size: 13px;
    line-height: 23px;
  }

  &.vue-fine-uploader-dropzone-active {
    cursor: pointer;
    background-color: #0a98bb;
  }
}

.attachmentContainer,
.embeddedImagesContainer {
  .card-body {
    .rowContainer {
      &:nth-child(2) {
        margin-top: -10px;
      }

      &:nth-child(odd) {
        background-color: #bddfe6;
      }

      .row:last-child {
        margin-bottom: 10px;
      }

      .row {
        margin-top: 5px;
        margin-left: 0;
        margin-right: 0;
        line-height: 30px;

        img {
          max-height: 32px;
          max-width: 100%;
          margin-bottom: 10px;

          &:hover {
            cursor: pointer;
          }
        }

        div.buttons {
          float: right;
          text-align: right;

          button {
            margin-left: 5px;

            &.notClickable {
              cursor: default;

              &:hover {
                background-color: #e8e8e8;
              }
            }
          }
        }
      }
    }
  }
}
</style>
