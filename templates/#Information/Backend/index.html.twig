{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/information/information.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/information/information.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block ng_init_body %}id="information" ng-controller="InformationBackendController" ng-init="section='{{ module }}'; load();"{% endblock %}

{% block administration %}
    {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' %}
        {% block administration %}
            {{ parent() }}

            {% if workflow_can(information, 'create') %}
                <div class="sidebar-panel">
                    <h5 class="sidebar-panel-title">
                        {{ "Administration"|trans({}, 'information') }}
                    </h5>
                </div>

                <div class="content" >
                    <a href="{{ path('abus_information_administration_create', {'section': module, 'domain': getDomain(), 'subdomain': getSubdomain('portal') }) }}" class="btn btn-success btn-block mr5">{{ "Create new information"|trans({}, 'information') }}</a>
                </div>
            {% endif %}
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block headline %}{{ "_headline"|trans({}, module) }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {% set content %}

        <div id="loading" ng-show="waitingForPublishingLoading"></div>

        <div id="salesInformation" ng-cloak>
            <div class="container-fluid salesInformations">
                <div ng-repeat="result in waitingForPublishing track by $index">
                    {% include 'Information/Parts/row_information.html.twig' %}
                </div>
            </div>
        </div>
    {% endset %}

    {% include 'Frame/TwigTemplates/portlet.html.twig' with {
        'id': false,
        'class': 'success mt20',
        'heading': true,
        'iconClass': false,
        'topic': "Waiting for publishing"|trans({}, 'information'),
        'content': content,
        'footer': false,
        'iconRefresh': false,
        'iconToggle': true,
        'iconClose': false,
        'plain': false,
        'movable': false,
        'closedByDefault': false
    } only %}

    {% set content %}

        <div id="loading" ng-show="waitingForTranslationLoading"></div>

        <div id="salesInformation" ng-cloak>
            <div class="container-fluid salesInformations">
                <div ng-repeat="result in waitingForTranslation track by $index">
                    {% include 'Information/Parts/row_information.html.twig' %}
                </div>
            </div>
        </div>
    {% endset %}

    {% include 'Frame/TwigTemplates/portlet.html.twig' with {
        'id': false,
        'class': 'warning mt20',
        'heading': true,
        'iconClass': false,
        'topic': "Waiting for translation"|trans({}, 'information'),
        'content': content,
        'footer': false,
        'iconRefresh': false,
        'iconToggle': true,
        'iconClose': false,
        'plain': false,
        'movable': false,
        'closedByDefault': false
    } only %}

    {% set content %}

        <div id="loading" ng-show="draftsLoading"></div>

        <div id="salesInformation" ng-cloak>
            <div class="container-fluid salesInformations">
                <div ng-repeat="result in drafts track by $index">
                    {% include 'Information/Parts/row_information.html.twig' %}
                </div>
            </div>
        </div>
    {% endset %}

    {% include 'Frame/TwigTemplates/portlet.html.twig' with {
        'id': false,
        'class': 'danger mt20',
        'heading': true,
        'iconClass': false,
        'topic': "Drafts"|trans({}, 'information'),
        'content': content,
        'footer': false,
        'iconRefresh': false,
        'iconToggle': true,
        'iconClose': false,
        'plain': false,
        'movable': false,
        'closedByDefault': false
    } only %}

{% endblock %}

{% block custom_javascripts %}{% endblock %}
