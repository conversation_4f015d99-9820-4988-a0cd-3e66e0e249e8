{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "User management"|trans({}, 'usermanagement') }} | ABUS Kransysteme GmbH{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/userManagement/userManagement.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/userManagement/userManagement.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block administration %}
    {{ parent() }}

    <div class="sidebar-panel">
    <h5 class="sidebar-panel-title">{{ "Administration"|trans({}, 'frame') }}</h5>
    </div>

    <div class="content">
        <a href="" class="btn btn-success btn-block mr5" data-toggle="modal" data-target="#indexModal" ng-click="">{{ "New company"|trans({}, 'usermanagement') }}</a>
        <a href="{{ path('abus_user_management_permission_packages') }}" class="btn btn-primary btn-block mr5">{{ "Permission packages"|trans({}, 'usermanagement') }}</a>
    </div>
{% endblock %}


{% block headline %}{{ 'Company Administration' }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}


{% block content %}

    <div ng-controller="usermanagementCompanyController" class="usermanagement">

        <form ng-show="result" ng-cloak>
            <div class="form-group mt10">
                <input type="text" id="filterTable" class="form-control" placeholder="{{ 'Filter companies by name'|trans({}, 'usermanagement') }}" ng-model="search" autocomplete="off" ng-change="companyView = [];">
            </div>
        </form>

        <table id="table-ajax-defer" class="table table-bordered resultTable" cellspacing="0" width="100%" ng-init="loadCompanies()" ng-show="result" ng-cloak>
            <thead>
            <tr>
                <th>{{ 'Company name'|trans({}, 'usermanagement') }}</th>
                <th>{{ 'Type'|trans({}, 'usermanagement') }}</th>
                <th>{{ 'Country'|trans({}, 'usermanagement') }}</th>
            </tr>
            </thead>

            <tbody>
                <tr ng-repeat-start="company in companies | companyNameFilter:search track by $index" ng-click="view($index, company.name)" ng-class="{greyBg: $index%2, redBg: !company.inActiveDirectory, notInDatabase: !company.inDatabase}" class="click">
                    <td ng-bind-html="company.name"></td>
                    <td ng-bind-html="company.type"></td>
                    <td ng-bind-html="company.country"></td>
                </tr>
                <tr ng-repeat-end ng-show="companyView[$index]">
                    <td colspan="3" style="padding: 0;">
                        <div class="viewCompany">
                            <button class="btn btn-primary btn-sm" style="float: right;">
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                                {{ 'Edit company'|trans({}, 'usermanagement') }}
                            </button>

                            <div>
                                {{ 'Address'|trans({}, 'usermanagement') }}:
                                {% verbatim %}{{ companyView[$index].data.street }}{% endverbatim %},
                                {% verbatim %}{{ companyView[$index].data.postcode }}{% endverbatim %}
                                {% verbatim %}{{ companyView[$index].data.town }}{% endverbatim %}
                            </div>
                            <div ng-bind-html="companyView[$index].data.telephone"></div>
                            <div ng-bind-html="companyView[$index].data.abukonfis_permissions"></div>




                            <h4>{{ 'Permission packages'|trans({}, 'usermanagement') }}</h4>
                            <table class="table table-striped table-bordered">
                                <tr>
                                    <th>{{ 'Module'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ 'Permission package'|trans({}, 'usermanagement') }}</th>
                                    <th style="border-right: 0 !important;">{{ 'AD Groups'|trans({}, 'usermanagement') }}</th>
                                    <th style="border-left: 0 !important;"><button class="btn btn-success btn-xs" style="float: right;" ng-click="addPermissionPackage()">
                                            <i class="fa fa-plus" aria-hidden="true"></i>
                                            {{ 'Add permission package'|trans({}, 'usermanagement') }}
                                        </button></th>
                                </tr>
                                <tr ng-repeat="permissionPackage in companyView[$index].data.permissionPackages">
                                    <td ng-bind-html="permissionPackage.module"></td>
                                    <td ng-bind-html="permissionPackage.name"></td>
                                    <td colspan="2" ng-bind-html="permissionPackage.adGroups"></td>
                                </tr>
                                <tr ng-show="showAddPermissionPackage">
                                    <td>
                                        <select class="form-control">
                                            <option></option>
                                            <option ng-repeat="permissionPackage in showAddPermissionPackage" ng-bind-html="permissionPackage.module"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <select class="form-control">
                                            <option>???</option>
                                        </select>
                                    </td>
                                    <td><button class="btn btn-success btn-sm">{{ 'Store'|trans({}, 'usermanagement') }}</button></td>
                                </tr>
                            </table>


                            <h4>{{ 'Open processes'|trans({}, 'usermanagement') }}</h4>

                            <table class="table table-striped table-bordered">
                                <tr>
                                    <th>{{ '???'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ '???'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ '???'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ '???'|trans({}, 'usermanagement') }}</th>
                                </tr>
                            </table>


                            <h4>{{ 'Users'|trans({}, 'usermanagement') }}</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th>{{ 'Last name'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ 'First name'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ 'eMail'|trans({}, 'usermanagement') }}</th>
                                    <th>{{ 'Last logon'|trans({}, 'usermanagement') }}</th>
                                </tr>
                                <tr ng-repeat-start="user in companyView[$index].users" ng-click="viewUser($index, user.email)" ng-class="{redBg: user.hasMorePermissionsThanCompany}">
                                    <td>
                                        {% verbatim %}{{ user.lastName }}{% endverbatim %}
                                        <i class="fa fa-user-secret" aria-hidden="true" title="User Manager" ng-if="user.isUsermanager"></i>
                                        <i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: red;" title="Has more permissions than company" ng-if="user.hasMorePermissionsThanCompany"></i>
                                    </td>
                                    <td ng-bind-html="user.firstName"></td>
                                    <td>
                                        {% verbatim %}{{ user.email }}{% endverbatim %}
                                        {#<i class="fa fa-envelope" aria-hidden="true" title="Send message"></i>#}
                                    </td>
                                    <td>
                                        <div ng-if="user.lastLogonTimestamp > 0">{% verbatim %}{{ user.lastLogonTimestamp*1000 | date:'yyyy-MM-dd HH:mm:ss' }}{% endverbatim %}</div>
                                    </td>
                                </tr>
                                <tr ng-repeat-end ng-show="userView[$index]">
                                    <td colspan="4" class="viewCompany">
                                        {% verbatim %}{{ userView[$index].firstName }}{% endverbatim %}
                                        {% verbatim %}{{ userView[$index].roles }}{% endverbatim %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>


        <div class="modal fade" id="indexModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">
                            <span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" id="mySmallModalLabel">{{ "Index database"|trans({}, 'tdata') }}</h4>
                    </div>
                    <div class="modal-body">

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" ng-click="">{{ "Cancel"|trans({}, 'frame') }}</button>
                        <button type="button" class="btn btn-success" ng-click="indexDatabase();">{{ "Index database"|trans({}, 'tdata') }}</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

{% endblock %}



{% block custom_javascripts %}{% endblock %}

{% block footer %}{% endblock %}
