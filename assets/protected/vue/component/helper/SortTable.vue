<template>
  <b-table
    :striped="striped"
    :bordered="bordered"
    :borderless="borderless"
    :outlined="outlined"
    :small="small"
    :hover="hover"
    :dark="dark"
    :fixed="fixed"
    :foot-clone="footClone"
    :no-border-collapse="noCollapse"
    :head-variant="headVariant"
    :table-variant="tableVariant"
    :items="items"
    sort-icon-left
    responsive="sm"
    sticky-header
    hover
    :fields="fields">
    <template v-for="name in slotNames" v-slot:[name]="scope">
      <slot :name="name" v-bind="scope" />
    </template>
  </b-table>
</template>

<script setup lang="ts">
import { getCurrentInstance, useSlots, computed } from 'vue';

interface SortTableProps {
  items?: any[];
  fields?: any[];
  headVariant?: string;
  tableVariant?: string;
  fixed?: boolean;
  striped?: boolean;
  bordered?: boolean;
  borderless?: boolean;
  outlined?: boolean;
  small?: boolean;
  hover?: boolean;
  dark?: boolean;
  footClone?: boolean;
  noCollapse?: boolean;
}

withDefaults(defineProps<SortTableProps>(), {
  items: () => [],
  fields: () => [],
  headVariant: '',
  tableVariant: '',
  fixed: false,
  striped: false,
  bordered: false,
  borderless: false,
  outlined: false,
  small: false,
  hover: false,
  dark: false,
  footClone: false,
  noCollapse: false,
});

/**
 * Vue 2.7: scoped slots live on proxy.$scopedSlots
 * Vue 3: all slots (incl. scoped) are functions on useSlots()
 * We compute the union of keys so this works in both.
 */
const { proxy } = getCurrentInstance()!;
const slots = useSlots();
const slotNames = computed(() => {
  const v2 = proxy && (proxy as any).$scopedSlots ? Object.keys((proxy as any).$scopedSlots) : [];
  const v3 = slots ? Object.keys(slots) : [];
  // Prefer Vue 2 scopedSlot keys if present; otherwise fall back to Vue 3 slot keys
  return v2.length ? v2 : v3;
});
</script>

<style lang="scss" scoped>
* {
  font-size: 0.8em;
}

.b-table-sticky-header {
  overflow-y: auto;
  max-height: 58em;
}
</style>
