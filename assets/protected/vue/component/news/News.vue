<template>
  <card card-class="dark mt20">
    <template #title>
      <span v-if="archive" v-html="t('frame/dashboard/headline_archive')"></span>
      <span v-else v-html="t('frame/dashboard/headline')"></span>
    </template>

    <template #body>
      <Spinner v-if="loadingNews" size="medium" :message="t('apis/loading')" />

      <div
        v-for="newsItem in myNews"
        :key="newsItem.news.id"
        class="bs-callout bs-callout-info w-100"
        :class="{ archive }">
        <h4>
          <div class="container-fluid">
            <div class="row">
              <div class="col-sm-12 col-md-7 col-lg-8 p-0" v-html="newsItem.news.title" />
              <div
                class="col-md-4 col-lg-3 d-none d-md-block p-0 publishdate"
                v-html="convertTimestampToDate(newsItem.news.publishdate)" />
              <div class="col-md-1 col-lg-1 d-none d-md-block p-0">
                <button v-if="!archive && !backend" class="close" type="button" @click="markedAsRead(newsItem.news.id)">
                  ×
                </button>
              </div>
            </div>
          </div>
        </h4>

        <div class="newscontent" v-html="newsItem.news.content" />

        <div v-for="file in newsItem.news.files" :key="file.name" class="attachment">
          <a :href="`/news/attachment/${newsItem.news.id}/${file.name}`" target="_blank">
            <img class="icon" :src="`/images/fileIcons/32px/${file.extension}.png`" />
          </a>
          <a :href="`/news/attachment/${newsItem.news.id}/${file.name}`" target="_blank" v-html="file.filename" />
        </div>

        <div v-if="backend" class="badges">
          <span v-for="module in newsItem.news.modules" :key="module.name" class="badge badge-success mr10 mb10">
            <span v-if="module.parentName" v-html="`${t(module.parentName)} / ${t(module.name)}`" />
            <span v-else v-html="t(module.name)" />
          </span>
          <span
            v-for="group in newsItem.news.groups"
            :key="group"
            class="badge badge-primary mr10 mb10"
            v-html="group" />
        </div>

        <div>
          <a
            v-if="admin"
            class="btn btn-xs btn-abus-blue-dark"
            :href="`/news/edit/${newsItem.news.id}`"
            style="float: right"
            v-html="t('frame/news/edit')" />
        </div>
      </div>

      <a
        v-if="!loadingNews && !archive && !backend"
        class="btn btn-abus-blue-light btn-block mr5 mb10"
        href="/news/archive"
        v-html="t('frame/news/archive')" />
    </template>
  </card>
</template>

<script lang="ts" setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import axios from 'axios';

import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import Card from '@assets/protected/vue/component/helper/Card.vue';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';
import { useModalStore } from '@assets/protected/vue/stores';

const props = defineProps<{
  archive?: boolean;
  admin?: boolean;
  backend?: boolean;
}>();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;
const globalLocale = proxy.$i18n.locale;

const { languageToLocale } = useLanguageHelpers();
const { convertTimestampToDate } = useDateHelpers(globalLocale);

const modalStore = useModalStore();

const loadingNews = ref(true);
const myNews = ref<any[]>([]);
const locale = ref(languageToLocale(globalLocale));

const loadNews = async () => {
  loadingNews.value = true;
  let url = '/news/dashboard';
  if (props.archive) url = '/news/dashboard/archive';
  if (props.backend) url = '/news/backend';

  try {
    const response = await axios.get(url);
    myNews.value = response.data;
  } catch (error: any) {
    modalStore.showModal({
      cssClass: 'error',
      message: error.response?.data || 'Error loading news.',
    });
  } finally {
    loadingNews.value = false;
  }
};

const markedAsRead = async (id: number) => {
  try {
    await axios.get(`/news/read/${id}`);
    await loadNews();
  } catch (err: any) {
    console.error(err);
  }
};

onMounted(() => {
  loadNews();
});
</script>

<style lang="scss">
div.newscontent {
  text-align: justify;

  img {
    max-width: 100%;
  }
}
</style>

<style scoped lang="scss">
div.bs-callout {
  display: table;

  div.attachment {
    margin-top: 15px;
  }

  div.publishdate {
    font-size: 0.8rem;
    text-align: right;
    color: #989898;
    position: relative;
    top: 5px;
  }

  .btn {
    margin-top: 10px;
  }

  &.archive div.publishdate {
    margin-right: 0;
  }
}

div.badges {
  margin-top: 15px;
}
</style>
