<div class="row">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h4 class="panel-title">{{ 'References'|trans({}, 'information') }}</h4>
        </div>
        <div class="panel-body">
            <div class="container-fluid" ng-init="addReferenceIfEmpty()">

                <div class="singleReference" ng-repeat="(key, reference) in references track by $index">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" ng-model="references[key].titleWithId" trigger-typeahead ng-click="referenceClick($event, search)" ng-class="{'stored': visualizeStore['reference_{% verbatim %}{{ $index }}{% endverbatim %}'], 'loading':searching}" typeahead-wait-ms="400" placeholder="{{ "What are you looking for?"|trans({}, 'information') }}" uib-typeahead="title as title.title for title in getReferences($viewValue)" typeahead-popup-template-url="/information/directive/search" typeahead-loading="searching" typeahead-on-select="onSelectReference($item, $model, $label, $index)" typeahead-focus-first="true" typeahead-min-length="1" typeahead-editable="false" autocomplete="off">
                        </div>

                        <div class="col-md-2">
                            <div class="checkboxContainer blue pl10" ng-class="{active: references[key].replacement}">
                                <div class="checkbox-custom" style="height: 34px;">
                                    <input type="checkbox" name="replacement[{% verbatim %}{{ $index }}{% endverbatim %}]" value="true" id="replacement[{% verbatim %}{{ $index }}{% endverbatim %}]" ng-model="references[key].replacement" ng-blur="save('referenceReplacement' + $index)" ng-disabled="!references[$index]">
                                    <label for="replacement[{% verbatim %}{{ $index }}{% endverbatim %}]" style="line-height: 34px;">{{ 'Replacement'|trans({}, 'information') }}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-push-2 col-md-2" style="text-align: right;">
                            <button class="btn btn-danger btn-md" ng-click="deleteReference($index)" ng-show="references[$index]">
                                <i class="fa fa-trash-o" aria-hidden="true"></i>&nbsp;&nbsp;&nbsp;{{ 'Delete reference'|trans({}, 'information') }}
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-addon"><img src="{{ asset('dist/images/flags/de.png') }}" /></span>
                                <input type="text" id="referenceReasonGer{% verbatim %}{{ $index }}{% endverbatim %}" class="form-control" ng-model="references[key].ger" placeholder="{{ 'Reason'|trans({}, 'information') }}" ng-blur="save('referenceReasonGer' + $index)" ng-class="{'stored': visualizeStore['referenceReasonGer{% verbatim %}{{ $index }}{% endverbatim %}']}" ng-disabled="!references[$index]">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-addon"><img src="{{ asset('dist/images/flags/gb.png') }}" /></span>
                                <input type="text" id="referenceReasonEng{% verbatim %}{{ $index }}{% endverbatim %}" class="form-control" ng-model="references[key].eng" placeholder="{{ 'Reason'|trans({}, 'information') }}" ng-blur="save('referenceReasonEng' + $index)" ng-class="{'stored': visualizeStore['referenceReasonEng{% verbatim %}{{ $index }}{% endverbatim %}']}" ng-disabled="!references[$index]">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-addon"><img src="{{ asset('dist/images/flags/fr.png') }}" /></span>
                                <input type="text" id="referenceReasonFre{% verbatim %}{{ $index }}{% endverbatim %}" class="form-control" ng-model="references[key].fre" placeholder="{{ 'Reason'|trans({}, 'information') }}" ng-blur="save('referenceReasonFre' + $index)" ng-class="{'stored': visualizeStore['referenceReasonFre{% verbatim %}{{ $index }}{% endverbatim %}']}" ng-disabled="!references[$index]">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-addon"><img src="{{ asset('dist/images/flags/es.png') }}" /></span>
                                <input type="text" id="referenceReasonEsl{% verbatim %}{{ $index }}{% endverbatim %}" class="form-control" ng-model="references[key].esl" placeholder="{{ 'Reason'|trans({}, 'information') }}" ng-blur="save('referenceReasonEsl' + $index)" ng-class="{'stored': visualizeStore['referenceReasonEsl{% verbatim %}{{ $index }}{% endverbatim %}']}" ng-disabled="!references[$index]">
                            </div>
                        </div>

                    </div>

                    <hr>
                </div>

                <div class="row" style="margin-bottom: 0;">
                    <div class="col-md-12 addReference">
                        <button class="btn btn-success btn-sm" ng-click="addReference()">
                            <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>&nbsp;&nbsp;&nbsp;{{ 'Add reference'|trans({}, 'information') }}
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
