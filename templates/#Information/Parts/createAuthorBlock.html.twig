<div class="row">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h4 class="panel-title">{{ 'Author'|trans({}, 'information') }}</h4>
        </div>
        <div class="panel-body" style="display: block;">

            <div class="row" style="margin-bottom: 0;">
                <div class="col-md-4">
                    <div class="input-group input-icon">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-user s16"></i></span>
                        <input type="text" id="authorName" class="form-control" ng-model="data.author.name" ng-blur="save('authorName')" ng-class="{'stored': visualizeStore['authorName']}" placeholder="{{ 'Name'|trans({}, 'information') }}">
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="input-group input-icon">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-envelope s16"></i></span>
                        <input type="text" id="authorEmail" class="form-control" ng-model="data.author.email" ng-blur="save('authorEmail')" ng-class="{'stored': visualizeStore['authorEmail']}" placeholder="{{ 'eMail'|trans({}, 'information') }}">
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="input-group input-icon">
                        <span class="input-group-addon"><i class="glyphicon glyphicon-earphone s16"></i></span>
                        <input type="text" id="authorPhone" class="form-control" ng-model="data.author.phone" ng-blur="save('authorPhone')" ng-class="{'stored': visualizeStore['authorPhone']}" placeholder="{{ 'Phone extension number'|trans({}, 'information') }}">
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
