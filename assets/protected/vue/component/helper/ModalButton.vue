<template>
  <b-button class="btn-block btn-sm mr5" :variant="variant" @click="showModal">
    {{ title }}
  </b-button>
</template>

<script setup lang="ts">
import { onUnmounted, watch } from 'vue';
import { useModalStore } from '@assets/protected/vue/stores';

interface ModalButtonProps {
  variant?: string;
  cssClass?: string;
  title?: string;
  href?: string;
  modalTitle?: string;
  modalMessage?: string;
  buttons?: any;
}

const props = withDefaults(defineProps<ModalButtonProps>(), {
  variant: 'primary',
  cssClass: 'primary',
  buttons: false,
});

const modalStore = useModalStore();

const showModal = () => {
  modalStore.showModal({
    cssClass: props.cssClass,
    title: props.modalTitle,
    message: props.modalMessage || '',
    buttons: props.buttons,
  });
};

// Watch for modal click events
watch(
  () => modalStore.getClick,
  (value: any) => {
    if (value === 'ok' && props.href) {
      window.location.href = props.href;
    }
  },
);
</script>
