{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('dist/protected/abus/information/information.js') }}"></script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('dist/protected/abus/information/information.css') }}" rel="stylesheet" type="text/css">
{% endblock %}


{% block ng_init_body %}id="information" ng-controller="InformationController" ng-init="section='{{ module }}'; loadFavorites();"{% endblock %}

{% block administration %}
    {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' %}
        {% block administration %}
            {{ parent() }}
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block headline %}{{ "My favorites"|trans({}, 'information') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div id="salesInformation" ng-cloak>
        <div class="container-fluid salesInformations">
            <div ng-repeat="result in results track by $index">
                {% include 'Information/Parts/row_information.html.twig' %}
            </div>
        </div>
    </div>

{% endblock %}

{% block custom_javascripts %}{% endblock %}
