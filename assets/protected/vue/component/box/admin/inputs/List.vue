<template>
  <div class="row">
    <div class="label col-sm-3" v-html="t(label)"></div>

    <div class="list col-sm-4">
      <li v-for="(element, index) in value" :key="index">
        <span class="badge badge-secondary">
          {{ element.toUpperCase() }}
          <button class="btn btn-xs btn-danger" :title="t('box/admin/remove')" @click="remove(index)">
            <i class="fas fa-trash" aria-hidden="true"></i>
          </button>
        </span>
      </li>

      <button
        v-if="!addElement"
        class="btn btn-xs btn-abus-blue-light add"
        @click="addElement = true"
        v-html="t('box/admin/add')"></button>

      <input
        v-if="addElement"
        v-model="elementValue"
        class="form-control form-control-sm"
        type="text"
        :placeholder="t(placeholder)" />
      <button v-if="addElement" class="btn btn-sm btn-success" @click="add()" v-html="t('box/admin/save')"></button>
      <button
        v-if="addElement"
        class="btn btn-sm btn-danger"
        @click="cancel()"
        v-html="t('box/admin/cancel')"></button>
    </div>

    <div class="description col-sm-5" v-html="t(description)"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import { useBoxStore } from '@assets/protected/vue/stores';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const boxStore = useBoxStore();

interface Props {
  label: string;
  description: string;
  placeholder: string;
  inode: number;
  category: string;
  setting: string;
}

const props = defineProps<Props>();

const getValue = computed(() => boxStore.getValue);
const setValue = (payload: any) => boxStore.setValue(payload);

const value = ref<any>([]);
const addElement = ref<boolean>(false);
const elementValue = ref<string>('');

const add = () => {
  value.value.push(elementValue.value);
  setValue({ inode: props.inode, category: props.category, setting: props.setting, value: value.value });
  cancel();
};

const remove = (index: number) => {
  value.value.splice(index, 1);
  setValue({ inode: props.inode, category: props.category, setting: props.setting, value: value.value });
};

const cancel = () => {
  elementValue.value = '';
  addElement.value = false;
};

onMounted(() => {
  value.value = getValue.value(props.inode, props.category, props.setting);
  if (value.value === null) value.value = [];
});
</script>

<style lang="scss" scoped>
.row {
  .label {
    font-weight: bold;
    line-height: 30px;
  }

  .list {
    li {
      list-style: none;

      margin-bottom: 5px;

      &:last-of-type {
        margin-bottom: 10px;
      }
    }

    input {
      margin-bottom: 10px;
    }

    .badge {
      padding: 3px 8px;
      min-width: 300px;
      text-align: left;
      line-height: 20px;

      button {
        margin-left: 10px;
        float: right;
      }
    }

    button {
      &.add {
        margin: 2px 0 5px 0;
      }
    }
  }

  .description {
    font-size: 0.8rem;
  }
}
</style>
