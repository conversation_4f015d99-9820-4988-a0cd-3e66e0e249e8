<template>
  <div class="row">
    <div class="label col-sm-3" v-html="t(label)"></div>

    <div class="list col-sm-4">
      <div class="input-group">
        <div class="input-group-prepend">
          <div class="input-group-text">
            <i class="fa fa-calendar"></i>
          </div>
          <!-- TODO: Replace date-picker component -->
          <!--          <date-picker
            v-model="value"
            :config="config"
            :wrap="true"
            @dp-change="setValue({ inode: inode, category: category, setting: setting, value: value })"
            style="border-left: none; border-top-left-radius: 0; border-bottom-left-radius: 0"></date-picker>-->
        </div>
      </div>
    </div>

    <div class="description col-sm-5" v-html="t(description)"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import { useBoxStore } from '@assets/protected/vue/stores';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;
const locale = proxy.$i18n.locale;

const boxStore = useBoxStore();

interface Props {
  label: string;
  description: string;
  inode: number;
  category: string;
  setting: string;
}

const props = defineProps<Props>();

const getValue = computed(() => boxStore.getValue);
const setValue = (payload: any) => boxStore.setValue(payload);

const value = ref<string>('');

onMounted(() => {
  value.value = getValue.value(props.inode, props.category, props.setting);
});

const config = ref({
  locale: dayjs.locale(locale),
  format: 'L',
  showClear: true,
  useCurrent: false,
  allowInputToggle: true,
  icons: {
    time: 'fa fa-clock-o',
    date: 'fa fa-calendar',
    up: 'fa fa-chevron-up',
    down: 'fa fa-chevron-down',
    previous: 'fa fa-chevron-left',
    next: 'fa fa-chevron-right',
    today: 'fa fa-sun-o',
    clear: 'fa fa-trash',
    close: 'fa fa-remove',
  },
});
</script>

<style lang="scss" scoped>
.row {
  margin-bottom: 20px;

  .label {
    font-weight: bold;
    line-height: 30px;
  }

  .description {
    font-size: 0.8rem;
  }
}
</style>
