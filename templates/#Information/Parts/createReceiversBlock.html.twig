<div class="row">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h4 class="panel-title">{{ "Receivers"|trans({}, 'information') }}</h4>
        </div>
        <div class="panel-body" style="display: block;">

            <div class="row">
                <div class="col-md-3" ng-repeat="(key, receiver) in data.available_receivers track by $index">
                    <div class="checkbox-custom">
                        <input type="checkbox" value="true" id="receiver_{% verbatim %}{{ $index }}{% endverbatim %}" ng-model="data.receivers[key]" ng-change="save('receiver_{% verbatim %}{{ $index }}{% endverbatim %}')">
                        <label for="{receiver_{% verbatim %}{{ $index }}{% endverbatim %}" ng-bind-html="receiver" ng-class="{'stored': visualizeStore['receiver_{% verbatim %}{{ $index }}{% endverbatim %}']}"></label>
                    </div>
                </div>
            </div>

            <div class="row mb0">
                <div class="col-md-3">
                    <button class="btn btn-warning" ng-click="previewReceiversList()">
                        {{ "View receivers list"|trans({}, 'information') }}
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>
