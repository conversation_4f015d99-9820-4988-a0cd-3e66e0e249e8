{% set languages = {'de': {'language': 'German', 'flag': 'de' }, 'gb': {'language': 'English', 'flag': 'gb' }, 'fr': {'language': 'French', 'flag': 'fr' }, 'es': {'language': 'Spanish', 'flag': 'es' } } %}


<div id="translateAndPublishModal">

    <table>
        {% for key, language in languages %}
            <tr>
                <td>
                    <div class="checkbox-custom">
                        <input type="checkbox" name="{{ key|lower }}" value="true" id="language{{ key|capitalize }}" data-ng-model="mydata.{{ key|lower }}">
                        <label for="language{{ key|capitalize }}"></label>
                    </div>
                </td>
                <td><img src="{{ asset('dist/images/flags/') }}{{ language.flag }}.png" /></td>
                <td>{{ language.language|trans({}, 'information') }}</td>
            </tr>
        {% endfor %}
    </table>

</div>

