<template>
  <div>
    <div id="inlineSearch">
      <div class="row">
        <div
          class="col-12 pb-1"
          :class="{ 'col-md-4': documentationMode === true, 'col-md-6': documentationMode === false }">
          <div v-if="documentationMode === false">
            <typeahead-input
              :src="'/box/nextcloud/search?folder=' + folderPath + '&language=' + languageTld"
              query-param-name="search"
              :placeholder="t('box/search/placeholder')"
              :responseAttr="['filenameURLDecoded', 'extension', 'languages', 'pathOnlyURLDecoded']"
              :disableReset="true"
              :nothingFoundText="t('box/search/nothing_found')"
              @hit="onSelectFile"
              cssStyle="box"
              popperPosition="bottom-start"
              :delay="500"
              :limit="200"
              :serverLimited="true">
              <!--TODO: Die Slots können angepasst werden mit eigenem CSS und co.
                                mit slot-scope wird ein Objekt definiert, über welches man auf den Child-Scope zugreifen kann.
                                Properties je Slot:
                                limited slot: items_count (Anzahl der angezeigten Ergebnisse), max_items (Gesamtzahl der Ergebnisse)
                                empty slot: - bislang keine Properties -
                                item slot: item, $item (aktueller Index), items_count, attributes (array - gewählte Antwort-Attribute)
                                -->
              <template v-slot:limited="slot">
                <li
                  class="limitSlot"
                  @mousedown.prevent
                  v-html="
                    t('box/search/limited')
                      .replace('#LIMIT#', slot.items_count)
                      .replace('#FOUND#', slot.max_items_server)
                  "></li>
              </template>
            </typeahead-input>
          </div>

          <div v-if="documentationMode === true">
            <typeahead-input
              :src="
                '/box/nextcloud/searchDocumentation?folder=' +
                folderPath +
                '&documentationMode=' +
                documentationMode +
                '&currentOnly=' +
                currentOnly +
                '&archiveOnly=' +
                archiveOnly +
                '&language=' +
                languageTld
              "
              query-param-name="search"
              :placeholder="t('box/search/placeholder')"
              :responseAttr="['filenameURLDecoded', 'extension', 'languages', 'pathOnlyURLDecoded']"
              :disableReset="true"
              :nothingFoundText="t('box/search/nothing_found')"
              @hit="onSelectFile"
              cssStyle="box"
              popperPosition="bottom-start"
              delay="500"
              limit="200"
              :serverLimited="true">
              <!--TODO: Die Slots können angepasst werden mit eigenem CSS und co.
                                mit slot-scope wird ein Objekt definiert, über welches man auf den Child-Scope zugreifen kann.
                                Properties je Slot:
                                limited slot: items_count (Anzahl der angezeigten Ergebnisse), max_items (Gesamtzahl der Ergebnisse)
                                empty slot: - bislang keine Properties -
                                item slot: item, $item (aktueller Index), items_count, attributes (array - gewählte Antwort-Attribute)
                                -->
              <template v-slot:limited="slot">
                <li
                  class="limitSlot"
                  @mousedown.prevent
                  v-html="
                    t('box/search/limited')
                      .replace('#LIMIT#', slot.items_count)
                      .replace('#FOUND#', slot.max_items_server)
                  "></li>
              </template>
            </typeahead-input>
          </div>
        </div>

        <div class="col-12 col-md-3 pb-1">
          <div class="dropdown">
            <button
              id="dropdownLanguage"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="languageFlag + language"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownLanguage">
              <a
                class="dropdown-item"
                style="font-weight: bold"
                @click="selectLanguage({ name: t('box/search/all_languages'), tld: null })">
                {{ t('box/search/all_languages') }}
              </a>
              <a v-for="language in languages" class="dropdown-item" @click="selectLanguage(language)">
                <img class="flag" :src="getLanguageFlagPath(language.tld)" />&nbsp;&nbsp;{{ language.name }}
              </a>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-3 pb-1">
          <div class="dropdown">
            <button
              id="dropdownFolder"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="folderTranslation"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownFolder">
              <a
                v-if="documentationMode === false"
                class="dropdown-item"
                style="font-weight: bold"
                @click="
                  selectFolder({ filename: t('box/search/all_folders'), fullpath: '/' }, t('box/search/all_folders'))
                "
                >{{ t('box/search/all_folders') }}</a
              >
              <a
                v-if="documentationMode"
                class="dropdown-item"
                style="font-weight: bold"
                @click="selectDocumentFolder('', t('box/search/all_folders'))"
                >{{ t('box/search/all_folders') }}</a
              >
              <a
                v-for="folder in folders"
                v-if="documentationMode === false && folder.isDir"
                class="dropdown-item"
                @click="selectFolder(folder, folder.filenameURLDecoded)"
                v-html="folder.filenameURLDecoded"></a>
              <a
                v-for="(folder, key) in folders"
                v-if="documentationMode"
                class="dropdown-item"
                @click="selectDocumentFolder(key, t(folder))"
                v-html="t(folder)"></a>
            </div>
          </div>
        </div>

        <div v-show="documentationMode" class="col-12 col-md-2 pb-1">
          <div class="dropdown">
            <button
              id="dropdownArchive"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="currentOrArchive"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownArchive">
              <a class="dropdown-item" style="font-weight: bold" @click="setCurrentOnly">{{
                t('box/search/currentOnly')
              }}</a>
              <a class="dropdown-item" @click="setArchiveOnly">{{ t('box/search/archiveOnly') }}</a>
              <a class="dropdown-item" @click="setCurrentAndArchive()">{{ t('box/search/currentAndArchive') }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import TypeaheadInput from '@assets/protected/vue/component/helper/TypeaheadInput.vue';
import { useBoxStore } from '@assets/protected/vue/stores';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';

interface Props {
  startFolder?: string | null;
  folders?: Array<any>;
  documentationMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  startFolder: null,
  folders: () => [],
  documentationMode: false,
});

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const boxStore = useBoxStore();
const { getLanguageFlagPath } = useLanguageHelpers();

const query = ref<string>('');
const folder = ref<string>('');
const folderPath = ref<string>('');
const folderTranslation = ref<string>(t('box/search/all_folders'));
const language = ref<string>('');
const languageTld = ref<string>('');
const languageFlag = ref<string>('');
const currentOnly = ref<boolean>(true);
const archiveOnly = ref<boolean>(false);
const currentOrArchive = ref<string>(t('box/search/currentOnly'));

const languages = ref([
  { name: t('language/brazilian'), tld: 'br' },
  { name: t('language/german'), tld: 'de' },
  { name: t('language/english'), tld: 'en' },
  { name: t('language/danish'), tld: 'dk' },
  { name: t('language/dutch'), tld: 'nl' },
  { name: t('language/czech'), tld: 'cz' },
  { name: t('language/finnish'), tld: 'fi' },
  { name: t('language/french'), tld: 'fr' },
  { name: t('language/greek'), tld: 'gr' },
  { name: t('language/italian'), tld: 'it' },
  { name: t('language/multi'), tld: 'eu' },
  { name: t('language/norwegian'), tld: 'no' },
  { name: t('language/polish'), tld: 'pl' },
  { name: t('language/swedish'), tld: 'se' },
  { name: t('language/spanish'), tld: 'es' },
  { name: t('language/turkish'), tld: 'tr' },
]);

/**
 * Callback Funktion die aufgerufen wird, wenn auf ein Suchergebnis geklickt wird
 *
 * @param file
 */
const onSelectFile = (file: any): void => {
  boxStore.openFolder(file.fullpathBase64encoded);
};

const setQuery = (queryValue: string): void => {
  query.value = queryValue;
};

const selectLanguage = (selectedLanguage: any): void => {
  language.value = selectedLanguage.name;
  languageTld.value = selectedLanguage.tld;
  languageFlag.value =
    selectedLanguage.tld !== null ? `<img src="${getLanguageFlagPath(selectedLanguage.tld)}" class="flag" />` : '';
};

const selectFolder = (selectedFolder: any, folderTranslationValue: string): void => {
  folderTranslation.value = folderTranslationValue;
  folder.value = selectedFolder.filename;
  folderPath.value = selectedFolder.fullpath;
};

const selectDocumentFolder = (selectedFolder: string, folderTranslationValue: string): void => {
  folderTranslation.value = folderTranslationValue;
  folder.value = selectedFolder;
  folderPath.value = selectedFolder;
};

const setArchiveOnly = (): void => {
  archiveOnly.value = true;
  currentOnly.value = false;
  currentOrArchive.value = t('box/search/archiveOnly');
};

const setCurrentOnly = (): void => {
  currentOnly.value = true;
  archiveOnly.value = false;
  currentOrArchive.value = t('box/search/currentOnly');
};

const setCurrentAndArchive = (): void => {
  currentOnly.value = false;
  archiveOnly.value = false;
  currentOrArchive.value = t('box/search/currentAndArchive');
};

onMounted(() => {
  folderPath.value = props.startFolder !== null ? props.startFolder : '/';
  folder.value = t('box/search/all_folders');
  language.value = t('box/search/all_languages');
});
</script>

<style lang="scss">
@import '@assets/scss/abstract/variables';

#inlineSearch {
  padding: 15px 10px;
  background-color: $abus-grey-7;
  color: $white;
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;

  .dropdown {
    button {
      height: 37px;
    }
    img {
      margin-top: -5px;
    }
  }
}

#file {
  padding: 15px 10px;
  border-top: #dfdfdf 1px solid;
  background-color: $abus-blue-light;
  color: $white;
  display: flow-root;

  div.filename {
    float: left;
    margin-right: 20px;
    line-height: 30px;
  }

  div.buttons {
    float: right;
  }

  button {
    margin-left: 15px;
  }
}

div#inlineSearch .dropdown button img {
  margin-top: -3px;
  margin-right: 10px;
}

div#inlineSearch .flag {
  width: 20px;
  height: 13px;
}
</style>
